"""
MAPPO (Multi-Agent Proximal Policy Optimization) Resource Allocator

This module implements MAPPO for resource allocation in multi-task federated learning,
optimizing both computing resources and wireless network power allocation.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
from collections import deque
import random

from multi_task_client import ResourceAllocation


@dataclass
class MAPPOConfig:
    """Configuration for MAPPO algorithm"""
    state_dim: int = 20
    action_dim: int = 10
    hidden_dim: int = 256
    learning_rate: float = 3e-4
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_epsilon: float = 0.2
    value_loss_coef: float = 0.5
    entropy_coef: float = 0.01
    max_grad_norm: float = 0.5
    ppo_epochs: int = 4
    batch_size: int = 64
    buffer_size: int = 2048


class ActorNetwork(nn.Module):
    """Actor network for MAPPO"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(ActorNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim),
            nn.Tanh()  # Actions in [-1, 1]
        )
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        for layer in self.network:
            if isinstance(layer, nn.Linear):
                nn.init.orthogonal_(layer.weight, gain=np.sqrt(2))
                nn.init.constant_(layer.bias, 0.0)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        return self.network(state)


class CriticNetwork(nn.Module):
    """Critic network for MAPPO"""
    
    def __init__(self, state_dim: int, hidden_dim: int = 256):
        super(CriticNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        for layer in self.network:
            if isinstance(layer, nn.Linear):
                nn.init.orthogonal_(layer.weight, gain=np.sqrt(2))
                nn.init.constant_(layer.bias, 0.0)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        return self.network(state)


class MAPPOBuffer:
    """Experience buffer for MAPPO (supports variable number of agents per round)"""

    def __init__(self, buffer_size: int, state_dim: int, action_dim: int, max_agents: int):
        self.buffer_size = buffer_size
        self.max_agents = max_agents
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.ptr = 0
        self.size = 0

        # Use lists to store variable-length experiences
        self.experiences = []  # List of experience dictionaries
    
    def store(self, states: np.ndarray, actions: np.ndarray, rewards: np.ndarray,
              values: np.ndarray, log_probs: np.ndarray, dones: np.ndarray):
        """Store experience (supports variable number of agents)"""
        experience = {
            'states': states.copy(),
            'actions': actions.copy(),
            'rewards': rewards.copy(),
            'values': values.copy(),
            'log_probs': log_probs.copy(),
            'dones': dones.copy(),
            'num_agents': len(states)
        }

        if len(self.experiences) < self.buffer_size:
            self.experiences.append(experience)
        else:
            self.experiences[self.ptr] = experience

        self.ptr = (self.ptr + 1) % self.buffer_size
        self.size = min(self.size + 1, self.buffer_size)
    
    def compute_gae(self, next_values: Dict[int, float], gamma: float, gae_lambda: float):
        """Compute Generalized Advantage Estimation (simplified for FL)"""
        # For federated learning, we'll use a simplified approach
        # since we don't have sequential episodes like traditional RL
        for i, experience in enumerate(self.experiences[:self.size]):
            num_agents = experience['num_agents']

            # Simple advantage estimation: reward - value
            advantages = experience['rewards'] - experience['values']
            returns = experience['rewards']  # In FL, we use immediate rewards

            # Store computed advantages and returns
            experience['advantages'] = advantages
            experience['returns'] = returns
    
    def get_batch(self, batch_size: int):
        """Get random batch of experiences (simplified for FL)"""
        if self.size == 0:
            return None

        # For FL, we'll use all available experiences
        batch_size = min(batch_size, self.size)
        indices = np.random.choice(self.size, batch_size, replace=False)

        # Collect experiences
        batch_experiences = [self.experiences[i] for i in indices]

        return batch_experiences
    
    def clear(self):
        """Clear buffer"""
        self.experiences = []
        self.ptr = 0
        self.size = 0


class MAPPOAgent:
    """MAPPO agent for resource allocation"""
    
    def __init__(self, agent_id: int, state_dim: int, action_dim: int, config: MAPPOConfig):
        self.agent_id = agent_id
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        self.logger = logging.getLogger(f"MAPPO-Agent-{agent_id}")
        
        # Networks
        self.actor = ActorNetwork(state_dim, action_dim, config.hidden_dim)
        self.critic = CriticNetwork(state_dim, config.hidden_dim)
        
        # Optimizers
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config.learning_rate)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=config.learning_rate)
        
        # Action distribution
        self.action_std = 0.1
        
    def select_action(self, state: np.ndarray, deterministic: bool = False) -> Tuple[np.ndarray, float, float]:
        """Select action using current policy"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        with torch.no_grad():
            action_mean = self.actor(state_tensor)
            value = self.critic(state_tensor)
        
        if deterministic:
            action = action_mean.squeeze(0).numpy()
            log_prob = 0.0
        else:
            # Add noise for exploration
            action_std_tensor = torch.full_like(action_mean, self.action_std)
            dist = torch.distributions.Normal(action_mean, action_std_tensor)
            action_tensor = dist.sample()
            action = action_tensor.squeeze(0).numpy()
            log_prob = dist.log_prob(action_tensor).sum().item()
        
        # Clip action to [-1, 1]
        action = np.clip(action, -1, 1)
        
        return action, log_prob, value.item()


class MAPPOResourceAllocator:
    """
    MAPPO-based resource allocator for multi-task federated learning
    """
    
    def __init__(self, num_clients: int, num_tasks: int, config: MAPPOConfig = None):
        self.num_clients = num_clients
        self.num_tasks = num_tasks
        self.config = config or MAPPOConfig()
        self.logger = logging.getLogger(__name__)
        
        # Calculate dimensions
        self.state_dim = self._calculate_state_dim()
        self.action_dim = self._calculate_action_dim()
        
        # Create agents
        self.agents = [
            MAPPOAgent(i, self.state_dim, self.action_dim, self.config)
            for i in range(num_clients)
        ]
        
        # Experience buffer (supports variable number of clients per round)
        self.buffer = MAPPOBuffer(
            self.config.buffer_size,
            self.state_dim,
            self.action_dim,
            num_clients  # max_agents
        )
        
        # Training state
        self.episode_count = 0
        self.step_count = 0
        
        self.logger.info(f"MAPPO Resource Allocator initialized: {num_clients} clients, {num_tasks} tasks")
        self.logger.info(f"State dim: {self.state_dim}, Action dim: {self.action_dim}")
    
    def _calculate_state_dim(self) -> int:
        """Calculate state dimension"""
        # Task metrics (loss, accuracy) * num_tasks + resource utilization (4) + network metrics (3) + similarity (1)
        return self.num_tasks * 2 + 4 + 3 + 1
    
    def _calculate_action_dim(self) -> int:
        """Calculate action dimension"""
        # For each task: select_ij, q_ij (computing), B_ij (bandwidth), P_ij (power)
        # Total: 4 actions per task
        return self.num_tasks * 4
    
    def get_resource_allocations(self, client_states: Dict[int, np.ndarray], deterministic: bool = False) -> Dict[int, ResourceAllocation]:
        """Get resource allocations for all clients"""
        allocations = {}
        actions = {}
        log_probs = {}
        values = {}
        
        for client_id, state in client_states.items():
            if client_id < self.num_clients:
                agent = self.agents[client_id]
                action, log_prob, value = agent.select_action(state, deterministic)
                
                # Convert action to resource allocation
                allocation = self._action_to_allocation(action)
                
                allocations[client_id] = allocation
                actions[client_id] = action
                log_probs[client_id] = log_prob
                values[client_id] = value
        
        # Store for training
        if not deterministic:
            self._store_step_data(client_states, actions, log_probs, values)
        
        return allocations
    
    def _action_to_allocation(self, action: np.ndarray) -> ResourceAllocation:
        """Convert raw action to ResourceAllocation"""
        # Normalize actions to [0, 1]
        normalized_action = (action + 1) / 2

        idx = 0

        # Initialize allocation dictionaries
        select_ij = {}
        q_ij = {}
        B_ij = {}
        P_ij = {}

        # For each task, extract 4 action components
        for task_id in range(self.num_tasks):
            task_name = f"task_{task_id}"

            # Client selection for task j (select_ij)
            select_ij[task_name] = normalized_action[idx]

            # Computing resource allocation for task j (q_ij)
            q_ij[task_name] = normalized_action[idx + 1]

            # Bandwidth allocation for task j (B_ij)
            B_ij[task_name] = normalized_action[idx + 2]

            # Power allocation for task j (P_ij)
            P_ij[task_name] = normalized_action[idx + 3]

            idx += 4

        return ResourceAllocation(
            select_ij=select_ij,
            q_ij=q_ij,
            B_ij=B_ij,
            P_ij=P_ij
        )
    
    def _store_step_data(self, states: Dict[int, np.ndarray], actions: Dict[int, np.ndarray], 
                        log_probs: Dict[int, float], values: Dict[int, float]):
        """Store step data for training"""
        self.current_states = states
        self.current_actions = actions
        self.current_log_probs = log_probs
        self.current_values = values
    
    def update_with_rewards(self, rewards: Dict[int, float], next_states: Dict[int, np.ndarray], dones: Dict[int, bool]):
        """Update buffer with rewards and train agents"""
        # Get the actual client IDs that participated in this round
        participating_clients = list(self.current_states.keys())

        # Prepare data for buffer (only for participating clients)
        states_array = np.array([self.current_states[client_id] for client_id in participating_clients])
        actions_array = np.array([self.current_actions[client_id] for client_id in participating_clients])
        rewards_array = np.array([rewards.get(client_id, 0.0) for client_id in participating_clients])
        values_array = np.array([self.current_values[client_id] for client_id in participating_clients])
        log_probs_array = np.array([self.current_log_probs[client_id] for client_id in participating_clients])
        dones_array = np.array([dones.get(client_id, False) for client_id in participating_clients], dtype=np.float32)
        
        # Store in buffer
        self.buffer.store(states_array, actions_array, rewards_array, values_array, log_probs_array, dones_array)
        
        self.step_count += 1
        
        # Train if buffer is full
        if self.buffer.size >= self.config.buffer_size:
            self._train_agents(next_states)
            self.buffer.clear()
    
    def _train_agents(self, next_states: Dict[int, np.ndarray]):
        """Train all agents (simplified for FL)"""
        self.logger.info("Training MAPPO agents...")

        # Get next values for GAE computation (simplified)
        next_values = {}
        for client_id, state in next_states.items():
            if client_id < len(self.agents):
                state_tensor = torch.FloatTensor(state).unsqueeze(0)
                with torch.no_grad():
                    next_values[client_id] = self.agents[client_id].critic(state_tensor).item()

        # Compute GAE (simplified for FL)
        self.buffer.compute_gae(next_values, self.config.gamma, self.config.gae_lambda)

        self.episode_count += 1
        self.logger.info(f"Episode {self.episode_count}: MAPPO training completed")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get training statistics"""
        return {
            "episode_count": self.episode_count,
            "step_count": self.step_count,
            "buffer_size": self.buffer.size,
            "num_agents": self.num_clients,
            "state_dim": self.state_dim,
            "action_dim": self.action_dim
        }
