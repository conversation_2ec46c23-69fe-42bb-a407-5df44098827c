import numpy as np
import pandas as pd
import torch
import torchvision
from sklearn.preprocessing import MinMaxScaler
from torchvision import transforms


def load_mnist(path, seed=None):
    random_seed = 1
    torch.backends.cudnn.enabled = False
    torch.manual_seed(random_seed)
    train_transform = transforms.Compose([
        transforms.Grayscale(num_output_channels=3),  # 将灰度图像转换为 3 通道 RGB
        transforms.Resize(224),  # 调整图像大小为 224x224
        transforms.ToTensor(),  # 转换为 Tensor
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),  # 标准化
    ])

    train_set = torchvision.datasets.MNIST(path, train=True, download=True, transform=train_transform)

    test_set = torchvision.datasets.MNIST(path, train=False, download=True, transform=train_transform)

    if seed is not None:
        pidx = np.random.RandomState(seed).permutation(len(train_set.data))
        train_set.data = train_set.data[pidx]
        train_set.targets = train_set.targets[pidx]
    return train_set, test_set


def load_cifar10(path, seed=None):
    random_seed = 1
    torch.backends.cudnn.enabled = False
    torch.manual_seed(random_seed)
    train_transform = transforms.Compose([
        transforms.Grayscale(num_output_channels=3),  # 将灰度图像转换为 3 通道 RGB
        transforms.Resize(224),  # 调整图像大小为 224x224
        transforms.ToTensor(),  # 转换为 Tensor
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),  # 标准化
    ])
    ## training data
    train_set = torchvision.datasets.CIFAR10(path, train=True, download=True, transform=train_transform)
    ## test data
    test_set = torchvision.datasets.CIFAR10(path, train=False, download=True, transform=train_transform)

    if seed is not None:
        pidx = np.random.RandomState(seed).permutation(len(train_set.data))
        train_set.data = train_set.data[pidx]
        train_set.targets = train_set.targets[pidx]
    return train_set, test_set


def load_air_quality(path='data/air_quality'):
    df = pd.read_csv(f'{path}/air_quality_preprocessed.csv')
    del df['Unnamed: 0']

    def timeseries_to_supervised(df_local, lag=1):
        #     df = pd.DataFrame(data)
        columns = [df_local.shift(i) for i in range(1, lag + 1)]
        columns.append(df_local)
        df_local = pd.concat(columns, axis=1)
        df_local.fillna(0, inplace=True)
        return df_local

    df_values = df.set_index('Date_Time')
    df_supervised = timeseries_to_supervised(df_values, 1)
    df_supervised.columns = ['Previous CO(GT)', 'Current CO(GT)']
    train, test = df_supervised[:7680], df_supervised[7680:]

    def scale(train, test):
        scaler = MinMaxScaler(feature_range=(-1, 1))
        scaler = scaler.fit(train)
        train_scaled = scaler.transform(train)
        test_scaled = scaler.transform(test)
        return scaler, train_scaled, test_scaled

    scaler, train_scaled, test_scaled = scale(train.values, test)

    def handle_data(data):
        class AirQualityDataset(torch.utils.data.Dataset):
            """Face Landmarks dataset."""

            def __init__(self, x, y):
                self.data = x
                self.targets = y

            def __len__(self):
                return len(self.data)

            def __getitem__(self, idx):
                return self.data[idx], self.targets[idx]

        X, Y = data[:, 0:-1], data[:, -1]
        X = X.astype(np.float32)
        Y = Y.astype(np.float32)
        # X = X.reshape(X.shape[0], 1, X.shape[1])
        return AirQualityDataset(X, Y)

    return handle_data(train_scaled), handle_data(test_scaled)


if __name__ == "__main__":
    train_scaled, test_scaled = load_air_quality('../data/air_quality')
    print('done')
