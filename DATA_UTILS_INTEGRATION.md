# Data Utils 集成说明

## 🎯 集成目标

**修正前**：在 `MultiTaskClient` 中重新实现数据加载逻辑

**修正后**：使用 `coldstart/data_utils.py` 中已有的标准化数据加载函数

## 🔄 集成后的数据加载流程

### 1. **使用 data_utils 中的标准函数**

#### 1.1 导入 data_utils 函数
```python
from coldstart.data_utils import load_mnist, load_cifar10, load_air_quality
```

#### 1.2 可用的数据加载函数
- `load_mnist(path, seed=None)` - 加载MNIST数据
- `load_cifar10(path, seed=None)` - 加载CIFAR-10数据  
- `load_air_quality(path='data/air_quality')` - 加载空气质量数据

### 2. **图像分类数据加载**

#### 2.1 MNIST 数据加载
```python
def _load_image_data_with_utils(self, task_name, task_config, load_mnist, load_cifar10):
    if 'mnist' in task_name.lower():
        # 使用data_utils中的load_mnist函数
        train_dataset, test_dataset = load_mnist(task_config.data_path, seed=self.client_id)
```

**data_utils 中的 MNIST 预处理**：
- 灰度图像转换为3通道RGB
- 调整图像大小为224x224
- 标准化：mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
- 支持随机种子用于数据洗牌

#### 2.2 CIFAR-10 数据加载
```python
if 'cifar' in task_name.lower():
    # 使用data_utils中的load_cifar10函数
    train_dataset, test_dataset = load_cifar10(task_config.data_path, seed=self.client_id)
```

**data_utils 中的 CIFAR-10 预处理**：
- 转换为灰度然后再转为3通道RGB
- 调整图像大小为224x224
- 相同的标准化参数
- 支持随机种子

### 3. **时间序列数据加载**

#### 3.1 Air Quality 数据加载
```python
def _load_time_series_data_with_utils(self, task_name, task_config, load_air_quality):
    if 'air' in task_name.lower() or 'quality' in task_name.lower():
        # 使用data_utils中的load_air_quality函数
        train_dataset, test_dataset = load_air_quality(task_config.data_path)
```

**data_utils 中的 Air Quality 预处理**：
- 读取 `air_quality_preprocessed.csv` 文件
- 创建时间序列监督学习格式（lag=1）
- 使用 MinMaxScaler 进行数据缩放（范围：-1到1）
- 自动分割训练集（前7680个样本）和测试集
- 返回自定义的 AirQualityDataset 类

### 4. **联邦学习数据分区**

#### 4.1 客户端数据子集创建
```python
# 为联邦学习创建客户端数据子集
client_data_size = min(1000, len(train_dataset) // 10)  # 每个客户端最多1000个样本
start_idx = (self.client_id * client_data_size) % len(train_dataset)
end_idx = min(start_idx + client_data_size, len(train_dataset))

client_indices = list(range(start_idx, end_idx))
client_train_dataset = Subset(train_dataset, client_indices)
```

#### 4.2 数据加载器创建
```python
# 创建数据加载器
batch_size = max(16, min(64, len(client_train_dataset) // 4))

train_loader = DataLoader(client_train_dataset, batch_size=batch_size, shuffle=True)
test_loader = DataLoader(client_test_dataset, batch_size=batch_size, shuffle=False)
```

## 📊 集成优势

### 1. **标准化预处理**
- ✅ 使用经过验证的数据预处理流程
- ✅ 统一的图像标准化参数
- ✅ 专业的时间序列处理

### 2. **一致性保证**
- ✅ 与现有 coldstart 项目保持一致
- ✅ 相同的数据格式和预处理
- ✅ 避免重复实现

### 3. **可维护性**
- ✅ 集中的数据处理逻辑
- ✅ 易于更新和修改
- ✅ 减少代码重复

### 4. **联邦学习支持**
- ✅ 客户端特定的数据分区
- ✅ 使用客户端ID作为随机种子
- ✅ 确保数据分布的一致性

## 🔗 与原有系统的兼容性

### 1. **数据格式兼容**
```python
# data_utils 返回的数据集格式
train_dataset, test_dataset = load_mnist(path, seed)

# 转换为 DataLoader（联邦学习需要）
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
```

### 2. **任务配置兼容**
```python
# 使用现有的 TaskConfig
task_config = TaskConfig(
    name="mnist",
    task_type=TaskType.IMAGE_CLASSIFICATION,
    data_path="data/MNIST",  # 传递给 data_utils 函数
    model_type="cnn"
)
```

### 3. **错误处理兼容**
```python
try:
    # 使用 data_utils 加载数据
    train_dataset, test_dataset = load_mnist(task_config.data_path, seed=self.client_id)
except Exception as e:
    self.logger.error(f"Failed to load data using data_utils: {e}")
    return None, None
```

## 🚀 实际使用示例

### 完整的数据加载流程
```python
class MultiTaskClient:
    def load_data(self):
        """在客户端初始化时加载所有任务的数据"""
        for task_name, task_config in self.task_configs.items():
            # 使用 data_utils 加载数据
            train_loader, test_loader = self._load_task_data(task_name, task_config)
            
            if train_loader is not None:
                self.task_data_loaders[task_name] = (train_loader, test_loader)
                self.logger.info(f"✓ Task '{task_name}' loaded using data_utils")
    
    def _load_task_data(self, task_name, task_config):
        """使用data_utils中的方法加载数据"""
        from coldstart.data_utils import load_mnist, load_cifar10, load_air_quality
        
        if task_config.task_type == TaskType.IMAGE_CLASSIFICATION:
            return self._load_image_data_with_utils(task_name, task_config, load_mnist, load_cifar10)
        elif task_config.task_type == TaskType.TIME_SERIES:
            return self._load_time_series_data_with_utils(task_name, task_config, load_air_quality)
```

## 📈 性能和质量提升

### 1. **数据质量**
- ✅ 经过验证的预处理流程
- ✅ 适合深度学习模型的数据格式
- ✅ 正确的标准化参数

### 2. **代码质量**
- ✅ 减少重复代码
- ✅ 提高可维护性
- ✅ 统一的错误处理

### 3. **系统一致性**
- ✅ 与 coldstart 项目保持一致
- ✅ 相同的数据处理标准
- ✅ 便于后续集成和扩展

## 🎯 总结

通过集成 `coldstart/data_utils.py`，我们实现了：

1. **标准化数据加载**：使用经过验证的数据预处理流程
2. **系统一致性**：与现有项目保持一致的数据格式
3. **联邦学习支持**：客户端特定的数据分区和加载
4. **代码复用**：避免重复实现数据加载逻辑
5. **易于维护**：集中的数据处理逻辑

这样的集成确保了多任务联邦学习系统使用高质量、标准化的数据，为后续的训练和评估提供了坚实的基础！
