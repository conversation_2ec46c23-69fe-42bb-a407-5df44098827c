#!/usr/bin/env python3
"""
Complete system test for multi-task federated learning with NS3 integration
"""

import logging
import numpy as np
import time
from typing import Dict

from mappo_allocator import MAPPOResourceAllocator, MAPPOConfig
from multi_task_client import MultiTaskClient, TaskConfig, TaskType, ResourceAllocation
from ns3_multi_task_integration import NS3MultiTaskSimulator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_clients(num_clients: int, task_configs: Dict[str, TaskConfig]) -> Dict[int, MultiTaskClient]:
    """Create test clients"""
    clients = {}
    
    for i in range(num_clients):
        client = MultiTaskClient(client_id=i, task_configs=task_configs)
        clients[i] = client
        logger.info(f"Created client {i}")
    
    return clients


def test_mappo_allocation(clients: Dict[int, MultiTaskClient], num_tasks: int) -> Dict[int, ResourceAllocation]:
    """Test MAPPO resource allocation"""
    logger.info("Testing MAPPO resource allocation...")
    
    # Create MAPPO allocator
    config = MAPPOConfig()
    allocator = MAPPOResourceAllocator(
        num_clients=len(clients),
        num_tasks=num_tasks,
        config=config
    )
    
    # Generate client states
    client_states = {}
    for client_id, client in clients.items():
        # Use a simple mock state for testing
        state = np.random.uniform(0, 1, allocator.state_dim)
        client_states[client_id] = state
    
    # Get resource allocations
    resource_allocations = allocator.get_resource_allocations(client_states, deterministic=True)
    
    logger.info(f"Generated resource allocations for {len(resource_allocations)} clients")
    
    # Log sample allocation
    if resource_allocations:
        sample_client = list(resource_allocations.keys())[0]
        sample_allocation = resource_allocations[sample_client]
        logger.info(f"Sample allocation for client {sample_client}:")
        
        for task_name in sample_allocation.select_ij:
            logger.info(f"  {task_name}: "
                       f"select={sample_allocation.select_ij[task_name]:.3f}, "
                       f"compute={sample_allocation.q_ij[task_name]:.3f}, "
                       f"bandwidth={sample_allocation.B_ij[task_name]:.3f}, "
                       f"power={sample_allocation.P_ij[task_name]:.3f}, "
                       f"priority={sample_allocation.Task_ij[task_name]:.3f}")
    
    return resource_allocations


def test_ns3_simulation(resource_allocations: Dict[int, ResourceAllocation], num_tasks: int):
    """Test NS3 network simulation"""
    logger.info("Testing NS3 network simulation...")
    
    # Create NS3 simulator
    simulator = NS3MultiTaskSimulator()
    
    try:
        # Start NS3 simulation
        logger.info("Starting NS3 simulation process...")
        if not simulator.start_ns3_simulation(
            num_clients=len(resource_allocations),
            num_tasks=num_tasks,
            tx_gain=5.0,
            network_type="wifi"
        ):
            logger.error("Failed to start NS3 simulation")
            return None
        
        # Connect to simulation
        logger.info("Connecting to NS3 simulation...")
        if not simulator.connect(timeout=15):
            logger.error("Failed to connect to NS3 simulation")
            return None
        
        # Run simulation
        logger.info("Running network simulation...")
        network_metrics = simulator.simulate_network(resource_allocations, num_tasks)
        
        # Log results
        logger.info("Network simulation results:")
        for client_id, metrics in network_metrics.items():
            logger.info(f"  Client {client_id}: "
                       f"throughput={metrics.throughput:.2f} Mbps, "
                       f"latency={metrics.latency:.2f} ms, "
                       f"round_time={metrics.round_time:.2f}s, "
                       f"packet_loss={metrics.packet_loss:.3f}")
        
        return network_metrics
        
    except Exception as e:
        logger.error(f"NS3 simulation failed: {e}")
        return None
        
    finally:
        simulator.close()


def test_client_selection_logic(resource_allocations: Dict[int, ResourceAllocation]):
    """Test client selection based on MAPPO decisions"""
    logger.info("Testing client selection logic...")
    
    selected_clients = []
    selection_threshold = 0.5
    
    for client_id, allocation in resource_allocations.items():
        # Calculate average selection probability
        avg_selection = sum(allocation.select_ij.values()) / len(allocation.select_ij)
        
        if avg_selection >= selection_threshold:
            selected_clients.append(client_id)
            logger.info(f"Client {client_id} selected (avg selection: {avg_selection:.3f})")
        else:
            logger.info(f"Client {client_id} not selected (avg selection: {avg_selection:.3f})")
    
    logger.info(f"Selected {len(selected_clients)} out of {len(resource_allocations)} clients")
    return selected_clients


def test_task_priority_analysis(resource_allocations: Dict[int, ResourceAllocation], num_tasks: int):
    """Test task priority analysis"""
    logger.info("Testing task priority analysis...")
    
    # Analyze task priorities across all clients
    task_priorities = {}
    for task_id in range(num_tasks):
        task_name = f"task_{task_id}"
        priorities = []
        
        for allocation in resource_allocations.values():
            if task_name in allocation.Task_ij:
                priorities.append(allocation.Task_ij[task_name])
        
        if priorities:
            avg_priority = sum(priorities) / len(priorities)
            max_priority = max(priorities)
            min_priority = min(priorities)
            
            task_priorities[task_name] = {
                'avg': avg_priority,
                'max': max_priority,
                'min': min_priority
            }
            
            logger.info(f"{task_name}: avg_priority={avg_priority:.3f}, "
                       f"max={max_priority:.3f}, min={min_priority:.3f}")
    
    # Find highest priority task overall
    if task_priorities:
        highest_priority_task = max(task_priorities.keys(), 
                                  key=lambda t: task_priorities[t]['avg'])
        logger.info(f"Highest priority task overall: {highest_priority_task}")
    
    return task_priorities


def main():
    """Main test function"""
    logger.info("="*80)
    logger.info("STARTING COMPLETE MULTI-TASK FEDERATED LEARNING SYSTEM TEST")
    logger.info("="*80)
    
    # Test parameters
    num_clients = 5
    num_tasks = 3
    
    # Create task configurations
    task_configs = {
        "task_0": TaskConfig("task_0", TaskType.IMAGE_CLASSIFICATION, "data/mnist", "cnn"),
        "task_1": TaskConfig("task_1", TaskType.IMAGE_CLASSIFICATION, "data/cifar10", "resnet"),
        "task_2": TaskConfig("task_2", TaskType.TIME_SERIES, "data/airquality", "gru")
    }
    
    try:
        # Step 1: Create clients
        logger.info("\n" + "="*50)
        logger.info("STEP 1: Creating Multi-Task Clients")
        logger.info("="*50)
        clients = create_test_clients(num_clients, task_configs)
        
        # Step 2: Test MAPPO allocation
        logger.info("\n" + "="*50)
        logger.info("STEP 2: Testing MAPPO Resource Allocation")
        logger.info("="*50)
        resource_allocations = test_mappo_allocation(clients, num_tasks)
        
        # Step 3: Test client selection logic
        logger.info("\n" + "="*50)
        logger.info("STEP 3: Testing Client Selection Logic")
        logger.info("="*50)
        selected_clients = test_client_selection_logic(resource_allocations)
        
        # Step 4: Test task priority analysis
        logger.info("\n" + "="*50)
        logger.info("STEP 4: Testing Task Priority Analysis")
        logger.info("="*50)
        task_priorities = test_task_priority_analysis(resource_allocations, num_tasks)
        
        # Step 5: Test NS3 simulation (optional - requires NS3 to be built)
        logger.info("\n" + "="*50)
        logger.info("STEP 5: Testing NS3 Network Simulation")
        logger.info("="*50)
        logger.info("NOTE: This step requires NS3 to be built and available")
        logger.info("If NS3 is not available, this step will be skipped")
        
        try:
            network_metrics = test_ns3_simulation(resource_allocations, num_tasks)
            if network_metrics:
                logger.info("NS3 simulation completed successfully!")
            else:
                logger.warning("NS3 simulation failed or not available")
        except Exception as e:
            logger.warning(f"NS3 simulation skipped due to error: {e}")
        
        # Summary
        logger.info("\n" + "="*50)
        logger.info("TEST SUMMARY")
        logger.info("="*50)
        logger.info(f"✓ Created {len(clients)} multi-task clients")
        logger.info(f"✓ Generated MAPPO resource allocations for {len(resource_allocations)} clients")
        logger.info(f"✓ Selected {len(selected_clients)} clients for participation")
        logger.info(f"✓ Analyzed priorities for {len(task_priorities)} tasks")
        logger.info("✓ All core components working correctly!")
        
        logger.info("\n" + "="*80)
        logger.info("COMPLETE MULTI-TASK FEDERATED LEARNING SYSTEM TEST COMPLETED")
        logger.info("="*80)
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
