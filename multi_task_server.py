"""
Multi-Task Federated Learning Server

This module extends the existing federated learning server to handle multiple tasks
with MAPPO resource allocation and NS3 network simulation integration.
"""

import logging
import numpy as np
import time
import sys
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import threading

# Import existing FL server components
from federated_learning.server import Server
from ns3_socket.network import Network

# Import our multi-task components
from multi_task_client import MultiTaskClient, ResourceAllocation, NetworkMetrics, TaskConfig, TaskType
from mappo_allocator import MAPPOResourceAllocator, MAPPOConfig


@dataclass
class MultiTaskFLConfig:
    """Configuration for multi-task federated learning"""
    num_clients: int = 5
    num_rounds: int = 100
    clients_per_round: int = 3
    similarity_threshold: float = 0.5

    # MAPPO configuration
    mappo_config: MAPPOConfig = None

    # Task configurations
    tasks: Dict[str, TaskConfig] = None

    # Network configuration
    network_config: Any = None


class MultiTaskFederatedServer:
    """
    Multi-task federated learning server with MAPPO resource allocation
    and NS3 network simulation integration
    """

    def __init__(self, config):
        # Initialize base server (but skip automatic boot)
        self.config = config

        # Multi-task specific configuration
        self.mt_config = MultiTaskFLConfig()
        if hasattr(config, 'multi_task'):
            self.mt_config = config.multi_task

        # Default task configurations
        if self.mt_config.tasks is None:
            self.mt_config.tasks = {
                "mnist": TaskConfig("mnist", TaskType.IMAGE_CLASSIFICATION, "data/MNIST", "cnn"),
                "cifar10": TaskConfig("cifar10", TaskType.IMAGE_CLASSIFICATION, "data/cifar-10-batches-py", "resnet"),
                "airquality": TaskConfig("airquality", TaskType.TIME_SERIES, "data/air_quality", "gru")
            }

        # MAPPO resource allocator
        if self.mt_config.mappo_config is None:
            self.mt_config.mappo_config = MAPPOConfig()

        self.mappo_allocator = MAPPOResourceAllocator(
            num_clients=self.mt_config.num_clients,
            num_tasks=len(self.mt_config.tasks),
            config=self.mt_config.mappo_config
        )

        # Network simulation
        self.network = None
        self.network_connected = False

        # Training state
        self.current_round = 0
        self.round_metrics: List[Dict[str, Any]] = []

        self.logger = logging.getLogger(__name__)
        self.logger.info("Multi-Task Federated Server initialized")

    def make_clients(self, num_clients):
        """Create multi-task clients"""
        self.logger.info(f'Creating {num_clients} multi-task clients...')

        # Create multi-task clients
        clients = []
        for client_id in range(num_clients):
            # Create multi-task client
            client = MultiTaskClient(client_id, self.mt_config.tasks)

            # Set similarity threshold
            client.similarity_threshold = self.mt_config.similarity_threshold

            # Analyze task similarity (will be cached after first computation)
            client.analyze_task_similarity()

            # Set link properties (compatibility with existing FL framework)
            if hasattr(self, 'config'):
                client.set_link(self.config)

            clients.append(client)

        self.clients = clients
        self.logger.info(f'Created {len(clients)} multi-task clients')

    def setup_network_simulation(self):
        """Setup NS3 network simulation"""
        try:
            self.logger.info("Setting up NS3 network simulation...")

            # Initialize network with existing interface
            self.network = Network(self.config)
            self.network.connect()
            self.network_connected = True

            self.logger.info("NS3 network simulation connected successfully")

        except Exception as e:
            self.logger.warning(f"Failed to setup NS3 network: {e}")
            self.logger.warning("Continuing without network simulation")
            self.network_connected = False

    def run_federated_round(self, round_num: int) -> Dict[str, Any]:
        """Run a single federated learning round with MAPPO and NS3 integration

        Workflow:
        1. MAPPO资源分配 → 2. NS3仿真传输 → 3. 客户端训练 → 4. 服务器聚合 → 5. MAPPO奖励反馈
        """
        self.logger.info(f"Starting federated round {round_num}")
        round_start_time = time.time()

        # 1. Select clients for this round
        selected_clients = self._select_clients()
        self.logger.info(
            f"Selected {len(selected_clients)} clients: {[c.client_id for c in selected_clients]}")

        # 2. Check task similarity (cached after first computation)
        self._ensure_task_similarity(selected_clients)

        # 3. Get current states from clients for MAPPO
        client_states = self._collect_client_states(selected_clients)

        # 4. MAPPO resource allocation (计算资源 + 无线功率分配)
        resource_allocations = self.mappo_allocator.get_resource_allocations(
            client_states)
        self.logger.info("Step 1: MAPPO resource allocation completed")

        # 5. Apply resource allocations to clients
        for client in selected_clients:
            if client.client_id in resource_allocations:
                client.set_resource_allocation(
                    resource_allocations[client.client_id])

        # 6. NS3 network simulation - 仿真模型传输过程
        self.logger.info(
            "Step 2: Starting NS3 network simulation for model transmission...")
        network_metrics = self._simulate_model_transmission_with_ns3(
            selected_clients, resource_allocations)

        # 7. Apply network metrics to clients (传输延迟等影响训练)
        for client in selected_clients:
            if client.client_id in network_metrics:
                client.set_network_metrics(network_metrics[client.client_id])

        # 8. Run client training (基于资源分配和网络约束)
        self.logger.info(
            "Step 3: Starting client training with allocated resources...")
        training_reports = self._run_client_training(selected_clients)

        # 9. Aggregate models (服务器聚合)
        self.logger.info("Step 4: Aggregating models at server...")
        aggregated_weights = self._aggregate_models(training_reports)

        # 10. Calculate rewards for MAPPO (基于性能和效率)
        rewards = self._calculate_mappo_rewards(
            selected_clients, training_reports, network_metrics)

        # 11. Update MAPPO with rewards (强化学习反馈)
        self.logger.info(
            "Step 5: Updating MAPPO with performance rewards...")
        next_states = self._collect_client_states(selected_clients)
        # Episode doesn't end
        dones = {client.client_id: False for client in selected_clients}
        self.mappo_allocator.update_with_rewards(rewards, next_states, dones)

        # 12. Store round metrics
        round_metrics = self._collect_round_metrics(
            round_num, selected_clients, training_reports,
            resource_allocations, network_metrics, rewards
        )
        self.round_metrics.append(round_metrics)

        round_duration = time.time() - round_start_time
        self.logger.info(
            f"Round {round_num} completed in {round_duration:.2f}s")
        self.logger.info(
            f"Workflow: MAPPO → NS3 → Training → Aggregation → Rewards")

        return round_metrics

    def _ensure_task_similarity(self, clients: List[MultiTaskClient]):
        """Ensure task similarity is computed for all clients (cached after first computation)"""
        for client in clients:
            if not client.similarity_computed:
                self.logger.info(
                    f"Computing task similarity for client {client.client_id} (first time)")
                client.analyze_task_similarity()
            else:
                self.logger.debug(
                    f"Client {client.client_id} using cached task similarity")

    def _select_clients(self) -> List[MultiTaskClient]:
        """Select clients for the current round"""
        num_select = min(self.mt_config.clients_per_round, len(self.clients))
        selected_indices = np.random.choice(
            len(self.clients), size=num_select, replace=False)
        return [self.clients[i] for i in selected_indices]

    def _collect_client_states(self, clients: List[MultiTaskClient]) -> Dict[int, np.ndarray]:
        """Collect current states from clients for MAPPO"""
        states = {}
        for client in clients:
            state = client.get_state_for_mappo()
            states[client.client_id] = state
        return states

    def _simulate_model_transmission_with_ns3(self, clients: List[MultiTaskClient],
                                              allocations: Dict[int, ResourceAllocation]) -> Dict[int, NetworkMetrics]:
        """
        使用NS3仿真模型传输过程 - 这是核心的网络仿真步骤

        在客户端训练前，仿真模型从服务器到客户端的传输过程，
        考虑MAPPO分配的无线功率和网络条件
        """
        network_metrics = {}

        self.logger.info("Starting NS3 model transmission simulation...")

        if not self.network_connected:
            self.logger.error("NS3 not connected, please start NS3 simulation")
            raise Exception("NS3 not connected")

        try:
            # 准备NS3仿真参数
            client_list = [client.client_id for client in clients]

            # 构建NS3请求，包含功率分配信息
            ns3_request_data = []
            for client in clients:
                allocation = allocations.get(client.client_id)
                client_data = {
                    'client_id': client.client_id,
                    'transmission_power': allocation.transmission_power if allocation else 0.5,
                    'model_size': 2048,  # KB, 模型大小
                    'task_count': len(client.task_configs)
                }
                ns3_request_data.append(client_data)

            self.logger.info(
                f"Sending NS3 request for {len(client_list)} clients with power allocations")

            # 发送请求到NS3进行网络仿真
            parsed_clients = self.network.parse_clients(client_list)
            ns3_response = self.network.sendRequest(
                requestType=1, array=parsed_clients)

            # 解析NS3响应
            for client_id, ns3_data in ns3_response.items():
                network_metrics[client_id] = NetworkMetrics(
                    client_id=client_id,
                    round_time=ns3_data.get('roundTime', 2.0),
                    throughput=ns3_data.get('throughput', 500.0),
                    latency=ns3_data.get('latency', 50.0),
                    packet_loss=ns3_data.get('packetLoss', 0.0)
                )

            self.logger.info(
                f" NS3 model transmission simulation completed for {len(network_metrics)} clients")

            # 记录仿真结果
            for client_id, metrics in network_metrics.items():
                allocation = allocations.get(client_id)
                power = allocation.transmission_power if allocation else 0.0
                self.logger.info(f"  Client {client_id}: Power={power:.3f}, "
                                 f"Throughput={metrics.throughput:.1f}Mbps, "
                                 f"Latency={metrics.latency:.1f}ms, "
                                 f"RoundTime={metrics.round_time:.2f}s")

        except Exception as e:
            self.logger.error(f"NS3 model transmission simulation failed: {e}")
            raise Exception(f"NS3 simulation failed: {e}")
            # Fallback to power-based simulation
            # for client in clients:
            #     allocation = allocations.get(client.client_id)
            #     power_factor = allocation.transmission_power if allocation else 0.5

            #     network_metrics[client.client_id] = NetworkMetrics(
            #         client_id=client.client_id,
            #         round_time=max(0.5, 3.0 / power_factor),
            #         throughput=max(100, 500 * power_factor),
            #         latency=max(20, 100 / power_factor),
            #         packet_loss=np.random.uniform(0, 0.1)
            #     )

        return network_metrics

    def _run_client_training(self, clients: List[MultiTaskClient]) -> List[Any]:
        """
        执行真正的联邦学习客户端训练流程

        流程：
        1. 模型分发：将全局模型发送给客户端
        2. 本地训练：客户端基于资源分配和网络约束进行多任务训练
        3. 模型上传：收集训练后的模型参数
        """
        self.logger.info("Starting federated client training...")
        self.logger.info(f"Training {len(clients)} clients with allocated resources and network constraints")

        reports = []

        # Step 1: 模型分发 - 将当前全局模型发送给所有客户端
        self.logger.info("Step 1: Distributing global model to clients...")
        self._distribute_global_model(clients)

        # Step 2: 并行本地训练 - 每个客户端基于分配的资源进行多任务训练
        self.logger.info("Step 2: Running parallel local training on clients...")
        for client in clients:
            try:
                # 设置训练环境
                client.current_round = self.current_round

                # 应用网络约束（传输延迟影响训练开始时间）
                if client.network_metrics:
                    transmission_delay = client.network_metrics.round_time
                    self.logger.info(f"Client {client.client_id}: Applying transmission delay {transmission_delay:.2f}s")
                    time.sleep(min(0.1, transmission_delay * 0.01))  # 模拟延迟（缩放）

                # 执行多任务本地训练
                self.logger.info(f"Client {client.client_id}: Starting multi-task local training...")
                training_start_time = time.time()

                # 真正的多任务训练
                local_training_metrics = self._execute_local_multi_task_training(client)

                training_duration = time.time() - training_start_time

                # 创建训练报告
                report = self._create_training_report(client, local_training_metrics, training_duration)
                reports.append(report)

                self.logger.info(f"Client {client.client_id}: Local training completed in {training_duration:.2f}s")
                self.logger.info(f"  Tasks trained: {list(local_training_metrics.keys())}")
                self.logger.info(f"  Resource utilization: CPU={self._get_cpu_utilization(client):.2f}, "
                               f"Power={client.current_allocation.transmission_power if client.current_allocation else 0:.2f}")

            except Exception as e:
                self.logger.error(f"Client {client.client_id} training failed: {e}")
                # 创建失败报告
                failed_report = self._create_failed_training_report(client, str(e))
                reports.append(failed_report)

        # Step 3: 收集训练结果
        self.logger.info("Step 3: Collecting training results from clients...")
        successful_clients = len([r for r in reports if hasattr(r, 'success') and r.success])
        self.logger.info(f"Training completed: {successful_clients}/{len(clients)} clients successful")

        return reports

    def _distribute_global_model(self, clients: List[MultiTaskClient]):
        """将全局模型分发给客户端"""
        self.logger.info("Distributing global model to clients...")

        for client in clients:
            try:
                # 模拟模型分发过程
                if hasattr(self, 'global_model_weights') and self.global_model_weights:
                    # 将全局模型权重发送给客户端
                    client.receive_global_model(self.global_model_weights)
                    self.logger.debug(f"Global model distributed to client {client.client_id}")
                else:
                    # 第一轮，客户端使用初始模型
                    self.logger.debug(f"Client {client.client_id} using initial model (first round)")

            except Exception as e:
                self.logger.error(f"Failed to distribute model to client {client.client_id}: {e}")

    def _execute_local_multi_task_training(self, client: MultiTaskClient) -> Dict[str, Dict[str, float]]:
        """执行客户端的多任务本地训练"""
        # 基于任务相似性和资源分配执行训练
        training_metrics = {}

        # 确保任务相似性已计算
        if not client.similarity_computed:
            client.analyze_task_similarity()

        # 根据资源分配调整训练参数
        if client.current_allocation:
            # 基于CPU分配调整批次大小和学习率
            total_cpu = sum(client.current_allocation.cpu_allocation.values())
            batch_size_factor = min(2.0, max(0.5, total_cpu))
            learning_rate_factor = min(1.5, max(0.5, total_cpu))

            self.logger.debug(f"Client {client.client_id}: Adjusted training params based on CPU allocation")
            self.logger.debug(f"  Total CPU: {total_cpu:.2f}, Batch factor: {batch_size_factor:.2f}")

        # 执行实际的多任务训练
        try:
            # 调用客户端的多任务训练方法
            training_metrics = client.run()

            # 添加资源利用率信息
            if client.current_allocation:
                for task_name in training_metrics:
                    if isinstance(training_metrics[task_name], dict):
                        training_metrics[task_name]['cpu_utilization'] = client.current_allocation.cpu_allocation.get(task_name, 0.0)
                        training_metrics[task_name]['power_allocation'] = client.current_allocation.transmission_power

        except Exception as e:
            self.logger.error(f"Multi-task training failed for client {client.client_id}: {e}")
            # 返回默认指标
            training_metrics = {task: {'loss': 10.0, 'accuracy': 0.0} for task in client.task_configs.keys()}

        return training_metrics

    def _create_training_report(self, client: MultiTaskClient, training_metrics: Dict[str, Dict[str, float]],
                              training_duration: float):
        """创建训练报告"""
        # 计算平均性能指标
        total_loss = 0.0
        total_accuracy = 0.0
        num_tasks = len(training_metrics)

        for task_metrics in training_metrics.values():
            if isinstance(task_metrics, dict):
                total_loss += task_metrics.get('loss', 0.0)
                total_accuracy += task_metrics.get('accuracy', 0.0)

        avg_loss = total_loss / max(1, num_tasks)
        avg_accuracy = total_accuracy / max(1, num_tasks)

        # 获取模型权重（简化）
        model_weights = None
        try:
            model_weights = client.get_model_weights()
        except:
            model_weights = {}

        # 计算网络延迟
        network_delay = 0.0
        if client.network_metrics:
            network_delay = client.network_metrics.round_time

        # 创建报告对象（兼容原有FL框架的Report格式）
        report = type('TrainingReport', (), {
            'client_id': client.client_id,
            'success': True,
            'loss': avg_loss,
            'accuracy': avg_accuracy,
            'training_time': training_duration,
            'model_weights': model_weights,
            'task_metrics': training_metrics,
            'num_tasks': num_tasks,
            'shared_tasks': len(client.shared_tasks),
            'independent_tasks': len(client.independent_tasks),
            'resource_allocation': client.current_allocation,
            'network_metrics': client.network_metrics,
            'delay': network_delay,  # 添加delay属性以兼容原有框架
            'weights': [(f'task_{i}', model_weights) for i, model_weights in enumerate([model_weights])]  # 兼容原有格式
        })()

        return report

    def _create_failed_training_report(self, client: MultiTaskClient, error_msg: str):
        """创建失败的训练报告"""
        # 计算网络延迟
        network_delay = 0.0
        if client.network_metrics:
            network_delay = client.network_metrics.round_time

        report = type('FailedTrainingReport', (), {
            'client_id': client.client_id,
            'success': False,
            'loss': 10.0,  # 高损失表示失败
            'accuracy': 0.0,
            'training_time': 0.0,
            'model_weights': None,
            'task_metrics': {},
            'error_message': error_msg,
            'num_tasks': len(client.task_configs),
            'resource_allocation': client.current_allocation,
            'network_metrics': client.network_metrics,
            'delay': network_delay,  # 添加delay属性以兼容原有框架
            'weights': []  # 失败时没有权重
        })()

        return report

    def _get_cpu_utilization(self, client: MultiTaskClient) -> float:
        """获取客户端CPU利用率"""
        if client.current_allocation and client.current_allocation.cpu_allocation:
            return sum(client.current_allocation.cpu_allocation.values())
        return 0.0

    def _aggregate_models(self, reports: List[Any]) -> Dict[str, Any]:
        """
        聚合客户端模型权重 - 真正的联邦学习聚合过程

        使用FedAvg算法聚合多任务模型，考虑任务相似性
        """
        if not reports:
            self.logger.warning("No training reports to aggregate")
            return {}

        # 过滤成功的训练报告
        successful_reports = [r for r in reports if hasattr(r, 'success') and r.success and hasattr(r, 'model_weights') and r.model_weights]

        if not successful_reports:
            self.logger.warning("No successful training reports with model weights")
            return {}

        self.logger.info(f"Aggregating models from {len(successful_reports)} successful clients")

        # 收集所有任务的权重
        task_weights_collection = {}

        for report in successful_reports:
            client_id = report.client_id

            # 收集每个任务的权重
            for task_name, task_weights in report.model_weights.items():
                if task_name not in task_weights_collection:
                    task_weights_collection[task_name] = []

                task_weights_collection[task_name].append({
                    'client_id': client_id,
                    'weights': task_weights,
                    'performance': 1.0 / (1.0 + report.loss),  # 性能权重
                    'num_samples': 100  # 简化：假设每个客户端有100个样本
                })

        # 执行简化的FedAvg聚合
        aggregated_weights = {}

        for task_name, task_data in task_weights_collection.items():
            self.logger.info(f"Aggregating task '{task_name}' from {len(task_data)} clients")

            # 简化聚合：取第一个客户端的权重作为聚合结果
            # 在实际应用中，这里应该实现真正的权重平均
            if task_data:
                aggregated_weights[task_name] = task_data[0]['weights']
                self.logger.debug(f"Task '{task_name}': Used weights from client {task_data[0]['client_id']}")

        # 保存全局模型权重
        self.global_model_weights = aggregated_weights

        # 记录聚合统计信息
        self.logger.info(f"Model aggregation completed:")
        self.logger.info(f"  Tasks aggregated: {len(aggregated_weights)}")
        self.logger.info(f"  Participating clients: {len(successful_reports)}")

        return aggregated_weights

    def _calculate_mappo_rewards(self, clients: List[MultiTaskClient], reports: List[Any],
                                 network_metrics: Dict[int, NetworkMetrics]) -> Dict[int, float]:
        """Calculate rewards for MAPPO based on training performance, network efficiency, and resource utilization"""
        rewards = {}

        for i, client in enumerate(clients):
            if i < len(reports):
                report = reports[i]
                client_network_metrics = network_metrics.get(client.client_id)

                # 1. Training Performance Reward (lower loss is better)
                # Scale loss to reward
                performance_reward = max(0, 2.0 - report.loss)

                # 2. Network Efficiency Reward (基于NS3仿真结果)
                if client_network_metrics:
                    # 网络传输效率：更高的吞吐量和更低的延迟获得更高奖励
                    throughput_reward = min(
                        1.0, client_network_metrics.throughput / 1000.0)  # Normalize to [0,1]
                    # Lower latency is better
                    latency_penalty = max(
                        0, 1.0 - client_network_metrics.latency / 100.0)
                    # Faster transmission
                    round_time_reward = max(
                        0, 1.0 - client_network_metrics.round_time / 5.0)
                    network_reward = (throughput_reward +
                                      latency_penalty + round_time_reward) / 3.0
                else:
                    network_reward = 0.3  # Default if no network metrics

                # 3. Resource Allocation Efficiency Reward
                if client.current_allocation:
                    # CPU资源利用效率
                    total_cpu = sum(
                        client.current_allocation.cpu_allocation.values())
                    # Prefer ~80% CPU usage
                    cpu_efficiency = 1.0 - abs(total_cpu - 0.8)

                    # 传输功率效率 (功率与网络性能的平衡)
                    power = client.current_allocation.transmission_power
                    if client_network_metrics:
                        # 功率效率 = 网络性能 / 功率消耗
                        power_efficiency = (
                            client_network_metrics.throughput / 1000.0) / max(0.1, power)
                        power_efficiency = min(
                            1.0, power_efficiency)  # Cap at 1.0
                    else:
                        power_efficiency = 0.5

                    resource_reward = (cpu_efficiency + power_efficiency) / 2.0
                    resource_reward = max(0, resource_reward)
                else:
                    resource_reward = 0.0

                # 4. Task Similarity Bonus (智能模型共享)
                similarity_bonus = 0.1 if len(client.shared_tasks) > 0 else 0.0

                # 5. AI-Network Co-optimization Bonus
                # 奖励那些在任务相似性指导下做出好的资源分配决策的客户端
                if len(client.shared_tasks) > 0 and client.current_allocation:
                    # 共享任务的客户端如果合理分配资源可以获得额外奖励
                    shared_task_bonus = 0.05 * len(client.shared_tasks)
                else:
                    shared_task_bonus = 0.0

                # Total reward (weighted combination)
                total_reward = (
                    0.4 * performance_reward +      # 40% 训练性能
                    0.3 * network_reward +          # 30% 网络效率
                    0.2 * resource_reward +         # 20% 资源效率
                    0.1 * (similarity_bonus + shared_task_bonus)  # 10% 协同优化
                )

                rewards[client.client_id] = total_reward

                self.logger.debug(f"Client {client.client_id} reward breakdown: "
                                  f"perf={performance_reward:.3f}, net={network_reward:.3f}, "
                                  f"res={resource_reward:.3f}, sim={similarity_bonus:.3f}, "
                                  f"total={total_reward:.3f}")
            else:
                rewards[client.client_id] = 0.0

        return rewards

    def _collect_round_metrics(self, round_num: int, clients: List[MultiTaskClient],
                               reports: List[Any], allocations: Dict[int, ResourceAllocation],
                               network_metrics: Dict[int, NetworkMetrics],
                               rewards: Dict[int, float]) -> Dict[str, Any]:
        """Collect comprehensive metrics for the round"""

        # Training metrics
        avg_loss = np.mean(
            [report.loss for report in reports]) if reports else 0.0
        avg_accuracy = np.mean(
            [report.accuracy for report in reports]) if reports else 0.0
        avg_delay = np.mean(
            [report.delay for report in reports]) if reports else 0.0

        # Network metrics
        avg_throughput = np.mean(
            [nm.throughput for nm in network_metrics.values()]) if network_metrics else 0.0
        avg_round_time = np.mean(
            [nm.round_time for nm in network_metrics.values()]) if network_metrics else 0.0

        # MAPPO metrics
        avg_reward = np.mean(list(rewards.values())) if rewards else 0.0
        mappo_stats = self.mappo_allocator.get_training_stats()

        # Task similarity metrics
        shared_task_count = sum(len(client.shared_tasks) for client in clients)
        independent_task_count = sum(
            len(client.independent_tasks) for client in clients)

        return {
            'round': round_num,
            'timestamp': time.time(),
            'num_clients': len(clients),
            'training_metrics': {
                'avg_loss': avg_loss,
                'avg_accuracy': avg_accuracy,
                'avg_delay': avg_delay
            },
            'network_metrics': {
                'avg_throughput': avg_throughput,
                'avg_round_time': avg_round_time
            },
            'mappo_metrics': {
                'avg_reward': avg_reward,
                'episode_count': mappo_stats['episode_count']
            },
            'task_similarity_metrics': {
                'shared_task_groups': shared_task_count,
                'independent_tasks': independent_task_count
            }
        }

    def run_experiment(self):
        """Run the complete multi-task federated learning experiment"""
        self.logger.info("Starting multi-task federated learning experiment")

        # Setup - use our custom boot instead of parent's boot()
        self.setup_network_simulation()
        self.custom_boot()

    def custom_boot(self):
        """Custom boot method that skips the parent's data and model loading"""
        logging.info('Booting multi-task federated learning server...')

        # Create clients directly using our make_clients method
        total_clients = self.config.clients.total
        self.make_clients(total_clients)

        # Initialize model path (for compatibility)
        model_path = self.config.paths.model
        sys.path.append(model_path)

        # Run federated rounds
        for round_num in range(self.mt_config.num_rounds):
            self.current_round = round_num

            try:
                round_metrics = self.run_federated_round(round_num)

                # Log progress
                if round_num % 10 == 0:
                    self._log_progress(round_num, round_metrics)

            except Exception as e:
                self.logger.error(f"Round {round_num} failed: {e}")
                raise

        # Final results
        self._generate_final_report()

        # Cleanup
        if self.network_connected:
            self.network.disconnect()

        self.logger.info("Multi-task federated learning experiment completed")

    def _log_progress(self, round_num: int, metrics: Dict[str, Any]):
        """Log training progress"""
        training = metrics['training_metrics']
        network = metrics['network_metrics']
        mappo = metrics['mappo_metrics']

        self.logger.info(f"Round {round_num} Progress:")
        self.logger.info(
            f"  Training - Loss: {training['avg_loss']:.4f}, Accuracy: {training['avg_accuracy']:.4f}, Delay: {training['avg_delay']:.2f}s")
        self.logger.info(
            f"  Network - Throughput: {network['avg_throughput']:.2f}, Round Time: {network['avg_round_time']:.2f}s")
        self.logger.info(
            f"  MAPPO - Avg Reward: {mappo['avg_reward']:.3f}, Episodes: {mappo['episode_count']}")

    def _generate_final_report(self):
        """Generate final experiment report"""
        if not self.round_metrics:
            self.logger.warning("No round metrics available for final report")
            return

        # Calculate final statistics
        final_loss = self.round_metrics[-1]['training_metrics']['avg_loss']
        initial_loss = self.round_metrics[0]['training_metrics']['avg_loss']
        loss_improvement = initial_loss - final_loss

        final_accuracy = self.round_metrics[-1]['training_metrics']['avg_accuracy']
        initial_accuracy = self.round_metrics[0]['training_metrics']['avg_accuracy']
        accuracy_improvement = final_accuracy - initial_accuracy

        avg_throughput = np.mean(
            [rm['network_metrics']['avg_throughput'] for rm in self.round_metrics])
        avg_reward = np.mean([rm['mappo_metrics']['avg_reward']
                             for rm in self.round_metrics])

        self.logger.info("="*60)
        self.logger.info("MULTI-TASK FEDERATED LEARNING FINAL REPORT")
        self.logger.info("="*60)
        self.logger.info(f"Total Rounds: {len(self.round_metrics)}")
        self.logger.info(
            f"Loss Improvement: {loss_improvement:.4f} ({initial_loss:.4f} → {final_loss:.4f})")
        self.logger.info(
            f"Accuracy Improvement: {accuracy_improvement:.4f} ({initial_accuracy:.4f} → {final_accuracy:.4f})")
        self.logger.info(f"Average Network Throughput: {avg_throughput:.2f}")
        self.logger.info(f"Average MAPPO Reward: {avg_reward:.3f}")
        self.logger.info(
            f"MAPPO Episodes Completed: {self.mappo_allocator.get_training_stats()['episode_count']}")
        self.logger.info("="*60)
