/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#ifndef MULTI_TASK_CLIENT_SESSION_H
#define MULTI_TASK_CLIENT_SESSION_H

#include <cstdint>
#include <map>
#include <string>
#include <memory>
#include "ns3/command-line.h"
#include "ns3/config.h"
#include "ns3/double.h"
#include "ns3/string.h"
#include "ns3/log.h"
#include "ns3/yans-wifi-helper.h"
#include "ns3/mobility-helper.h"
#include "ns3/ipv4-address-helper.h"
#include "ns3/yans-wifi-channel.h"
#include "ns3/mobility-model.h"
#include "ns3/internet-stack-helper.h"
#include "ns3/flow-monitor-helper.h"
#include "ns3/flow-monitor-module.h"
#include "ns3/ipv4-address.h"
#include "ns3/inet-socket-address.h"

namespace ns3 {
    class Socket;

    /**
     * \brief Resource allocation structure for multi-task FL
     */
    struct ResourceAllocation {
        std::map<std::string, double> select_ij;  // Task -> Selection probability [0,1]
        std::map<std::string, double> q_ij;       // Task -> Computing resource allocation [0,1]
        std::map<std::string, double> B_ij;       // Task -> Bandwidth allocation [0,1]
        std::map<std::string, double> P_ij;       // Task -> Power allocation [0,1]
        std::map<std::string, double> Task_ij;    // Task -> Task priority [0,1]
    };

    /**
     * \ingroup multi-task-client-session
     * \brief Multi-task client session to store information about the experiment
     */
    class MultiTaskClientSession {
    public:
        /**
         * \brief Construct multi-task client session
         * \param clientID_    Id for client
         * \param radius_      Radius Location for Client
         * \param theta_       Angular Location for Client (Radians)
         * \param numTasks_    Number of tasks
         */
        MultiTaskClientSession(int clientID_, double radius_, double theta_, int numTasks_);

        /**
         * \brief Gets the socket of client
         * \return Socket of client
         */
        Ptr<ns3::Socket> GetClient();

        /**
         * \brief Sets socket of client
         * \param client   Socket to associate with client
         */
        void SetClient(Ptr<ns3::Socket> client);

        /**
         * \brief Get if client is in round
         * \return Bool indicating if client is in round (true == inRound)
         */
        bool GetInRound();

        /**
         * \brief Set if client is in round
         * \param inRound    Bool indicating if client is in round
         */
        void SetInRound(bool inRound);

        /**
         * \brief Get radial location of client
         * \return Radial location of client
         */
        double GetRadius();

        /**
         * \brief Get angular location of client
         * \return Angular location of client
         */
        double GetTheta();

        /**
         * \brief Get id of client
         * \return Client id
         */
        int GetClientId();

        /**
         * \brief Get number of tasks
         * \return Number of tasks
         */
        int GetNumTasks();

        /**
         * \brief Set resource allocation for this client
         * \param allocation Resource allocation from MAPPO
         */
        void SetResourceAllocation(const ResourceAllocation& allocation);

        /**
         * \brief Get resource allocation for this client
         * \return Resource allocation
         */
        const ResourceAllocation& GetResourceAllocation() const;

        /**
         * \brief Get selection probability for a specific task
         * \param taskName Task name
         * \return Selection probability [0,1]
         */
        double GetTaskSelectionProbability(const std::string& taskName) const;

        /**
         * \brief Get computing resource allocation for a specific task
         * \param taskName Task name
         * \return Computing resource allocation [0,1]
         */
        double GetTaskComputeAllocation(const std::string& taskName) const;

        /**
         * \brief Get bandwidth allocation for a specific task
         * \param taskName Task name
         * \return Bandwidth allocation [0,1]
         */
        double GetTaskBandwidthAllocation(const std::string& taskName) const;

        /**
         * \brief Get power allocation for a specific task
         * \param taskName Task name
         * \return Power allocation [0,1]
         */
        double GetTaskPowerAllocation(const std::string& taskName) const;

        /**
         * \brief Get task priority for a specific task
         * \param taskName Task name
         * \return Task priority [0,1]
         */
        double GetTaskPriority(const std::string& taskName) const;

        /**
         * \brief Get total power allocation across all tasks
         * \return Total power allocation
         */
        double GetTotalPowerAllocation() const;

        /**
         * \brief Get average selection probability across all tasks
         * \return Average selection probability
         */
        double GetAverageSelectionProbability() const;

    private:
        ns3::Ptr<ns3::Socket> m_client;        //!< Socket of client
        double m_radius;                       //!< Radius location of client
        double m_theta;                        //!< Angular location of client
        int m_clientID;                        //!< Client id
        int m_numTasks;                        //!< Number of tasks
        bool m_inRound;                        //!< Indicates whether client should participate in round
        ResourceAllocation m_resourceAllocation; //!< Resource allocation from MAPPO
    };

    /**
     * \ingroup multi-task-client-session-manager
     * \brief Manages the multi-task client sessions
     */
    class MultiTaskClientSessionManager {
    public:
        /**
         * \brief Construct multi-task client session manager
         * \param clientSessions    map of < client ids, client sessions >
         */
        MultiTaskClientSessionManager(std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions);

        /**
         * \brief Get client id from client address
         * \param address  Client address
         * \return  Client id
         */
        int ResolveToId(ns3::Ipv4Address &address);

        /**
         * \brief Get client id from client socket
         * \param socket  Client socket
         * \return  Client id
         */
        int ResolveToIdFromServer(ns3::Ptr<ns3::Socket> socket);

        /**
         * \brief Close client socket
         */
        void Close();

        /**
         * \brief Get client address from client id
         * \param id  Client id
         * \return  Client address
         */
        ns3::Ipv4Address ResolveToAddress(int id);

        /**
         * \brief Get clients selected for a specific task
         * \param taskName Task name
         * \param threshold Selection threshold
         * \return List of selected client IDs
         */
        std::vector<int> GetSelectedClientsForTask(const std::string& taskName, double threshold = 0.5);

    private:
        std::map<ns3::Ipv4Address, int> m_clientSessionByAddress;                      //!< maps Client Address to Client id
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &m_clientSessionById; //!< maps client id to client session
        int m_nInRound;                                                                //!< number of clients in round
    };
}

#endif
