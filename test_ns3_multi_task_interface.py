#!/usr/bin/env python3
"""
Test interface for NS3 multi-task federated learning simulation
"""

import socket
import struct
import time
import numpy as np
import logging
from typing import Dict, List, Tuple
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ResourceAllocation:
    """Resource allocation from MAPPO"""
    select_ij: Dict[str, float]  # Task -> Selection probability [0,1]
    q_ij: Dict[str, float]       # Task -> Computing resource allocation [0,1]
    B_ij: Dict[str, float]       # Task -> Bandwidth allocation [0,1]
    P_ij: Dict[str, float]       # Task -> Power allocation [0,1]
    Task_ij: Dict[str, float]    # Task -> Task priority [0,1]


class NS3MultiTaskInterface:
    """Interface to communicate with NS3 multi-task FL simulation"""
    
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.socket = None
        
    def connect(self):
        """Connect to NS3 simulation"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            logger.info(f"Connected to NS3 simulation at {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to NS3 simulation: {e}")
            return False
    
    def send_command(self, command_type: int, n_clients: int, n_tasks: int):
        """Send command to NS3"""
        # Command structure: command(4 bytes) + nClients(4 bytes) + nTasks(4 bytes)
        command_data = struct.pack('III', command_type, n_clients, n_tasks)
        self.socket.send(command_data)
        logger.debug(f"Sent command: type={command_type}, clients={n_clients}, tasks={n_tasks}")
    
    def send_resource_allocations(self, allocations: Dict[int, ResourceAllocation], n_tasks: int):
        """Send resource allocations for all clients"""
        for client_id, allocation in allocations.items():
            # Send client ID
            self.socket.send(struct.pack('I', client_id))
            
            # Send allocation data (5 values per task: select_ij, q_ij, B_ij, P_ij, Task_ij)
            allocation_values = []
            for task_id in range(n_tasks):
                task_name = f"task_{task_id}"
                allocation_values.extend([
                    allocation.select_ij.get(task_name, 0.0),
                    allocation.q_ij.get(task_name, 0.0),
                    allocation.B_ij.get(task_name, 0.0),
                    allocation.P_ij.get(task_name, 0.0),
                    allocation.Task_ij.get(task_name, 0.0)
                ])
            
            # Send all allocation values as doubles
            for value in allocation_values:
                self.socket.send(struct.pack('d', value))
            
            logger.debug(f"Sent resource allocation for client {client_id}")
    
    def receive_results(self) -> Dict[int, Dict]:
        """Receive simulation results from NS3"""
        # Receive number of results
        n_results_data = self.socket.recv(4)
        n_results = struct.unpack('I', n_results_data)[0]
        
        results = {}
        for _ in range(n_results):
            # Receive client ID
            client_id_data = self.socket.recv(4)
            client_id = struct.unpack('I', client_id_data)[0]
            
            # Receive basic metrics (id, roundTime, throughput, latency, packetLoss)
            metrics_data = self.socket.recv(8 + 8 + 8 + 8 + 8)  # 5 doubles
            id_val, round_time, throughput, latency, packet_loss = struct.unpack('Qdddd', metrics_data)
            
            # Receive task-specific metrics
            n_task_metrics_data = self.socket.recv(4)
            n_task_metrics = struct.unpack('I', n_task_metrics_data)[0]
            
            task_metrics = {}
            for _ in range(n_task_metrics):
                # Receive task name length
                name_len_data = self.socket.recv(4)
                name_len = struct.unpack('I', name_len_data)[0]
                
                # Receive task name
                task_name = self.socket.recv(name_len).decode('utf-8')
                
                # Receive task metric value
                metric_value_data = self.socket.recv(8)
                metric_value = struct.unpack('d', metric_value_data)[0]
                
                task_metrics[task_name] = metric_value
            
            results[client_id] = {
                'id': id_val,
                'round_time': round_time,
                'throughput': throughput,
                'latency': latency,
                'packet_loss': packet_loss,
                'task_metrics': task_metrics
            }
            
            logger.debug(f"Received results for client {client_id}")
        
        logger.info(f"Received results for {n_results} clients")
        return results
    
    def run_simulation(self, allocations: Dict[int, ResourceAllocation], n_tasks: int) -> Dict[int, Dict]:
        """Run a complete simulation round"""
        n_clients = len(allocations)
        
        # Send RUN_SIMULATION command
        self.send_command(1, n_clients, n_tasks)  # 1 = RUN_SIMULATION
        
        # Send resource allocations
        self.send_resource_allocations(allocations, n_tasks)
        
        # Receive results
        results = self.receive_results()
        
        return results
    
    def close(self):
        """Close connection"""
        if self.socket:
            # Send EXIT command
            self.send_command(2, 0, 0)  # 2 = EXIT
            self.socket.close()
            logger.info("Connection closed")


def generate_sample_allocations(n_clients: int, n_tasks: int) -> Dict[int, ResourceAllocation]:
    """Generate sample resource allocations for testing"""
    allocations = {}
    
    for client_id in range(n_clients):
        select_ij = {}
        q_ij = {}
        B_ij = {}
        P_ij = {}
        Task_ij = {}
        
        for task_id in range(n_tasks):
            task_name = f"task_{task_id}"
            
            # Generate random allocations [0, 1]
            select_ij[task_name] = np.random.uniform(0.3, 1.0)  # Higher selection probability
            q_ij[task_name] = np.random.uniform(0.2, 0.8)
            B_ij[task_name] = np.random.uniform(0.2, 0.8)
            P_ij[task_name] = np.random.uniform(0.1, 0.6)
            Task_ij[task_name] = np.random.uniform(0.1, 1.0)
        
        allocations[client_id] = ResourceAllocation(
            select_ij=select_ij,
            q_ij=q_ij,
            B_ij=B_ij,
            P_ij=P_ij,
            Task_ij=Task_ij
        )
    
    return allocations


def test_ns3_interface():
    """Test the NS3 multi-task interface"""
    logger.info("Starting NS3 multi-task interface test")
    
    # Test parameters
    n_clients = 5
    n_tasks = 3
    
    # Generate sample allocations
    allocations = generate_sample_allocations(n_clients, n_tasks)
    
    logger.info(f"Generated allocations for {n_clients} clients and {n_tasks} tasks")
    
    # Print sample allocation
    client_0_alloc = allocations[0]
    logger.info("Sample allocation for client 0:")
    for task_id in range(n_tasks):
        task_name = f"task_{task_id}"
        logger.info(f"  {task_name}: select={client_0_alloc.select_ij[task_name]:.3f}, "
                   f"compute={client_0_alloc.q_ij[task_name]:.3f}, "
                   f"bandwidth={client_0_alloc.B_ij[task_name]:.3f}, "
                   f"power={client_0_alloc.P_ij[task_name]:.3f}, "
                   f"priority={client_0_alloc.Task_ij[task_name]:.3f}")
    
    # Create interface
    interface = NS3MultiTaskInterface()
    
    # Connect to NS3 (this will wait for NS3 to be running)
    logger.info("Attempting to connect to NS3 simulation...")
    logger.info("Make sure NS3 simulation is running with: ./waf --run wifi_exp_multi_fl")
    
    if not interface.connect():
        logger.error("Failed to connect to NS3. Make sure the simulation is running.")
        return
    
    try:
        # Run simulation
        logger.info("Running simulation...")
        results = interface.run_simulation(allocations, n_tasks)
        
        # Print results
        logger.info("Simulation results:")
        for client_id, result in results.items():
            logger.info(f"Client {client_id}:")
            logger.info(f"  Round time: {result['round_time']:.2f}s")
            logger.info(f"  Throughput: {result['throughput']:.2f} Mbps")
            logger.info(f"  Latency: {result['latency']:.2f} ms")
            logger.info(f"  Packet loss: {result['packet_loss']:.3f}")
            logger.info(f"  Task metrics: {result['task_metrics']}")
        
        logger.info("Test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during simulation: {e}")
    
    finally:
        interface.close()


if __name__ == "__main__":
    test_ns3_interface()
