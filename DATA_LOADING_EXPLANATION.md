# 客户端数据加载流程修正说明

## 🎯 问题识别

**之前的问题**：数据加载在训练时才进行，这不符合联邦学习的实际场景。

**修正目标**：数据应该在客户端创建时就加载完成，模拟真实的联邦学习环境。

## 🔄 修正后的数据加载流程

### 1. **客户端初始化时的数据加载** (`__init__` 阶段)

**新的初始化顺序**：
```python
def __init__(self, client_id, task_configs):
    # ... 基本初始化 ...
    
    # 数据存储
    self.task_data_loaders: Dict[str, Tuple] = {}  # {task_name: (train_loader, test_loader)}
    
    # 🔑 关键修正：在初始化时就加载所有数据
    self.load_data()
    
    # 然后初始化模型
    self._initialize_tasks()
    
    # 最后生成相似性缓存文件名
    self._generate_similarity_cache_filename(client_id)
```

### 2. **数据加载方法** (`load_data()`)

**目的**：在客户端创建时一次性加载所有任务的数据

**具体实现**：
```python
def load_data(self):
    """在客户端初始化时加载所有任务的数据"""
    self.logger.info(f"Client {self.client_id}: Loading data for all tasks...")
    
    for task_name, task_config in self.task_configs.items():
        try:
            # 加载任务数据
            train_loader, test_loader = self._load_task_data(task_name, task_config)
            
            if train_loader is not None and test_loader is not None:
                self.task_data_loaders[task_name] = (train_loader, test_loader)
                self.logger.info(f"✓ Task '{task_name}': {train_samples} train, {test_samples} test samples")
            else:
                self.logger.warning(f"✗ Failed to load data for task '{task_name}', will use simulation")
                self.task_data_loaders[task_name] = (None, None)
                
        except Exception as e:
            self.logger.error(f"Error loading data for task '{task_name}': {e}")
            self.task_data_loaders[task_name] = (None, None)
```

### 3. **支持的数据类型**

#### 3.1 图像分类数据
- **MNIST**: `data/MNIST/` 文件夹
- **CIFAR-10**: `data/cifar-10-batches-py/` 文件夹

```python
def _load_image_data(self, task_name: str, task_config):
    """加载图像分类数据 (MNIST, CIFAR-10)"""
    if 'mnist' in task_name.lower():
        # 加载MNIST数据
        train_dataset = torchvision.datasets.MNIST(
            root='data/MNIST', train=True, download=False, transform=transform
        )
        test_dataset = torchvision.datasets.MNIST(
            root='data/MNIST', train=False, download=False, transform=transform
        )
    elif 'cifar' in task_name.lower():
        # 加载CIFAR-10数据
        train_dataset = torchvision.datasets.CIFAR10(
            root='data/cifar-10-batches-py', train=True, download=False, transform=transform
        )
        test_dataset = torchvision.datasets.CIFAR10(
            root='data/cifar-10-batches-py', train=False, download=False, transform=transform
        )
```

#### 3.2 时间序列数据
- **Air Quality**: `data/air_quality/air_quality_preprocessed.csv` 文件

```python
def _load_time_series_data(self, task_name: str, task_config):
    """加载时间序列数据 (Air Quality)"""
    if 'air' in task_name.lower() or 'quality' in task_name.lower():
        # 加载空气质量数据
        df = pd.read_csv('data/air_quality/air_quality_preprocessed.csv')
        
        # 创建时间序列窗口
        window_size = 24  # 24小时窗口
        X, y = [], []
        
        for i in range(len(df) - window_size):
            X.append(df[feature_cols].iloc[i:i+window_size].values)
            y.append(df[target_col].iloc[i+window_size])
```

### 4. **联邦学习数据分区**

**每个客户端获得不同的数据子集**：
```python
# 为联邦学习创建客户端数据子集
client_data_size = min(1000, len(train_dataset) // 10)  # 每个客户端最多1000个样本
start_idx = (self.client_id * client_data_size) % len(train_dataset)
end_idx = min(start_idx + client_data_size, len(train_dataset))

client_indices = list(range(start_idx, end_idx))
client_train_dataset = Subset(train_dataset, client_indices)
```

### 5. **训练时使用预加载数据**

**修正后的训练方法**：
```python
def _simulate_task_training(self, task_name: str, epochs: int) -> Dict[str, float]:
    """真正的任务训练 - 使用预加载的数据"""
    
    # 使用预加载的数据
    if task_name in self.task_data_loaders:
        train_loader, test_loader = self.task_data_loaders[task_name]
        
        if train_loader is not None and test_loader is not None:
            self.logger.info(f"Using pre-loaded real data for task '{task_name}'")
            
            # 执行真正的训练
            training_metrics = self._execute_real_training(
                model, optimizer, train_loader, test_loader, epochs, task_name
            )
            
            return training_metrics
        else:
            # 回退到模拟训练
            return self._fallback_simulation(task_name, epochs)
```

## 📊 关键改进点

### 1. **符合联邦学习场景**
- ✅ 客户端在创建时就拥有本地数据
- ✅ 每个客户端有不同的数据子集
- ✅ 数据不会在训练过程中重新加载

### 2. **提高系统效率**
- ✅ 数据只加载一次，避免重复I/O
- ✅ 训练时直接使用预加载的数据
- ✅ 减少训练延迟

### 3. **更好的错误处理**
- ✅ 数据加载失败时有明确日志
- ✅ 自动回退到模拟训练
- ✅ 不会因为单个任务数据问题影响整个客户端

### 4. **真实数据支持**
- ✅ 支持MNIST图像分类
- ✅ 支持CIFAR-10图像分类
- ✅ 支持Air Quality时间序列
- ✅ 易于扩展支持更多数据类型

## 🔗 与联邦学习流程的集成

### 数据流程图
```
客户端创建
    ↓
load_data() - 加载所有任务数据
    ↓
_initialize_tasks() - 初始化模型
    ↓
训练开始
    ↓
_simulate_task_training() - 使用预加载数据训练
    ↓
返回训练结果
```

### 与服务器交互
1. **服务器创建客户端** → 客户端自动加载本地数据
2. **服务器分发模型** → 客户端使用预加载数据训练
3. **客户端上传结果** → 基于真实数据的训练结果

## 🚀 运行效果

修正后的数据加载流程实现了：

1. **真实的联邦学习环境**：每个客户端在创建时就拥有本地数据
2. **高效的训练过程**：数据预加载，训练时无需重新加载
3. **灵活的数据支持**：支持多种数据类型，易于扩展
4. **健壮的错误处理**：数据加载失败时自动回退

这样就真正模拟了**真实联邦学习场景中客户端拥有本地数据**的情况！
