/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#ifndef MULTI_TASK_FL_SIM_INTERFACE_H
#define MULTI_TASK_FL_SIM_INTERFACE_H

#include "ns3/command-line.h"
#include "ns3/config.h"
#include "ns3/double.h"
#include "ns3/string.h"
#include "ns3/log.h"
#include "ns3/yans-wifi-helper.h"
#include "ns3/mobility-helper.h"
#include "ns3/ipv4-address-helper.h"
#include "ns3/yans-wifi-channel.h"
#include "ns3/mobility-model.h"
#include "ns3/internet-stack-helper.h"
#include "ns3/flow-monitor-helper.h"
#include "ns3/flow-monitor-module.h"

#include "multi-task-client-session.h"
#include <unistd.h>
#include <stdio.h>
#include <sys/socket.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <string.h>
#include <map>
#include <memory>

namespace ns3 {

    /**
     * \ingroup multi-task-fl-sim-interface
     * \brief Interface to multi-task FL simulation
     */
    class MultiTaskFLSimProvider {
    public:

        /**
         * \brief Message used to communicate the results of a multi-task FL experiment.
         */
        struct Message {
            uint64_t id;
            double roundTime;
            double throughput;
            double latency;
            double packetLoss;
            std::map<std::string, double> taskMetrics; // Task-specific metrics
        };

        /**
         * \brief Resource allocation data received from Python MAPPO
         */
        struct ResourceAllocationData {
            uint32_t clientId;
            uint32_t numTasks;
            // For each task: select_ij, q_ij, B_ij, P_ij, Task_ij (5 values per task)
            std::vector<double> allocations; // Size = numTasks * 5
        };

        /**
         * \brief Command message used to communicate intent between
         * multi-task FL simulation and NS3 experiment
         */
        struct COMMAND {
            enum class Type : uint32_t {
                RESPONSE       = 0,
                RUN_SIMULATION = 1,
                EXIT           = 2,
                ENDSIM         = 3,
            };

            Type command;
            uint32_t nClients;
            uint32_t nTasks;
        };

        /**
         * \brief Constructor; listening port for the multi-task FL simulation
         *
         * \param port
         */
        MultiTaskFLSimProvider(uint16_t port) : m_port(port) {}

        /**
         * \brief Wait for multi-task FL simulation to connect.
         */
        void waitForConnection();

        /**
         * \brief Receive next experiment to run with resource allocations
         *
         * \param clientSessionMap Map of client id to MultiTaskClientSession
         * \return Command type
         */
        COMMAND::Type recv(std::map<int, std::shared_ptr<MultiTaskClientSession>> &clientSessionMap);

        /**
         * \brief Send the round results back to Python
         *
         * \param roundResultMap Map of client ID to results
         */
        void send(std::map<int, Message> &roundResultMap);

        /**
         * \brief Send end message
         */
        void end();

        /**
         * \brief Close socket if open
         */
        void Close();

    private:

        /**
         * \brief Receive resource allocation data for a single client
         *
         * \param clientId Client ID
         * \param numTasks Number of tasks
         * \return Resource allocation data
         */
        ResourceAllocationData receiveResourceAllocation(uint32_t clientId, uint32_t numTasks);

        /**
         * \brief Apply resource allocation to client session
         *
         * \param clientSession Client session to update
         * \param allocationData Resource allocation data
         */
        void applyResourceAllocation(std::shared_ptr<MultiTaskClientSession> clientSession,
                                   const ResourceAllocationData& allocationData);

        /**
         * \brief Convert allocation data to ResourceAllocation struct
         *
         * \param allocationData Raw allocation data
         * \return ResourceAllocation struct
         */
        ResourceAllocation convertToResourceAllocation(const ResourceAllocationData& allocationData);

        uint16_t m_port;              //!< Listening port number
        int m_server_fd;              //!< Listening socket file descriptor
        int m_new_socket;             //!< Session socket file descriptor
        struct sockaddr_in m_address; //!< Address data structure used to configure TCP socket.
    };
}

#endif
