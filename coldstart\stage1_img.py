import os
import torch
from torchvision import models
from torch.utils.data import DataLoader
import numpy as np

from coldstart import data_utils

tmp = 0


def extract_resnet_features(model, data_loader, device):
    features_arr = []
    with torch.no_grad():  # 禁用梯度计算，提高性能
        for images, _ in data_loader:
            # 通过模型提取特征
            images = images.to(device)
            output = model(images)
            features_arr.append(output.cpu().numpy())  # 将输出转为 numpy 数组并存储
            print('Extracted features shape:', output.shape)

    return np.concatenate(features_arr, axis=0)


def extract_img_data_set_name_features(data_name='mnist'):
    # 检查是否有可用的 GPU
    resnet_model = models.resnet50(pretrained=True)
    resnet_model.eval()  # 设置为评估模式
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    resnet_model.to(device)
    print('device = ', device)
    current_file_path = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file_path)
    train_set, test_set = None, None
    if 'mnist' in data_name.lower():
        train_set, test_set = data_utils.load_mnist(os.path.join(current_dir, '../data'))
    elif 'cifar10' in data_name.lower():
        train_set, test_set = data_utils.load_cifar10(os.path.join(current_dir, '../data'))
    assert train_set is not None and test_set is not None
    global tmp
    d_indices = torch.arange(tmp + 0, tmp + 1000)
    # tmp = tmp + 2000
    subset = torch.utils.data.Subset(train_set, d_indices)

    train_loader = DataLoader(subset, batch_size=64, shuffle=False)
    # 提取 MNIST 数据集的 ResNet 特征
    features = extract_resnet_features(resnet_model, train_loader, device)

    return features


if __name__ == "__main__":
    mnist_features = extract_img_data_set_name_features(data_name='cifar10')

    print("Extracted ResNet Features Shape:", mnist_features.shape)
