# Multi-Task Federated Learning NS3 Simulation Module

This module extends the original `wifi_exp` NS3 simulation to support multi-task federated learning with MAPPO resource allocation.

## Overview

The `wifi_exp_multi_fl` module simulates a wireless network environment for multi-task federated learning, where:

- **Multiple clients** participate in federated learning for **multiple tasks**
- **MAPPO (Multi-Agent Proximal Policy Optimization)** determines resource allocation
- **Resource allocation** includes:
  - `select_ij`: Client selection for each task
  - `q_ij`: Computing resource allocation
  - `B_ij`: Bandwidth allocation  
  - `P_ij`: Power allocation
  - `Task_ij`: Task priority

## Files Structure

```
wifi_exp_multi_fl/
├── main.cc                           # Main simulation entry point
├── multi-task-client-session.h       # Multi-task client session header
├── multi-task-client-session.cc      # Multi-task client session implementation
├── multi-fl-experiment.h             # Multi-task experiment header
├── multi-fl-experiment.cc            # Multi-task experiment implementation
├── multi-task-fl-sim-interface.h     # Socket interface header
├── multi-task-fl-sim-interface.cc    # Socket interface implementation
└── README.md                         # This file
```

## Key Features

### 1. Multi-Task Client Sessions
- Each client can participate in multiple tasks
- Resource allocation per task per client
- Task-specific performance metrics

### 2. MAPPO Resource Allocation Integration
- Receives resource allocation decisions from Python MAPPO
- Applies allocations to network configuration
- Supports dynamic client selection based on `select_ij`

### 3. Network Configuration
- **WiFi Power Control**: Transmission power based on `P_ij` allocation
- **Bandwidth Management**: Packet size and rate based on `B_ij` allocation
- **QoS Support**: Task priority-based quality of service using `Task_ij`

### 4. Socket Communication Protocol
- **Command Structure**: `{command_type, n_clients, n_tasks}`
- **Resource Data**: Per-client allocation arrays (5 values × n_tasks)
- **Results**: Network metrics + task-specific performance

## Building and Running

### 1. Build NS3 Module
```bash
cd ns3-fl-network
./waf configure
./waf build
```

### 2. Run Simulation
```bash
# Run with default parameters
./waf --run wifi_exp_multi_fl

# Run with custom parameters
./waf --run "wifi_exp_multi_fl --NumClients=10 --NumTasks=3 --TxGain=5.0"
```

### 3. Available Parameters
- `--NumClients`: Number of clients (default: 20)
- `--NumTasks`: Number of tasks (default: 3)
- `--NetworkType`: Network type - "wifi" or "ethernet" (default: "wifi")
- `--MaxPacketSize`: Maximum packet size in bytes (default: 1024)
- `--TxGain`: Transmission gain in dB (default: 0.0)
- `--ModelSize`: Model size in KB (default: 15.0)
- `--DataRate`: Application data rate (default: "250kbps")
- `--LearningModel`: "sync" or "async" (default: "sync")

## Python Integration

### 1. Socket Communication
The simulation listens on port 8080 for Python connections:

```python
from test_ns3_multi_task_interface import NS3MultiTaskInterface

# Connect to simulation
interface = NS3MultiTaskInterface(host='localhost', port=8080)
interface.connect()

# Run simulation with resource allocations
results = interface.run_simulation(allocations, n_tasks)
```

### 2. Resource Allocation Format
```python
allocation = ResourceAllocation(
    select_ij={"task_0": 0.8, "task_1": 0.6, "task_2": 0.9},
    q_ij={"task_0": 0.5, "task_1": 0.3, "task_2": 0.7},
    B_ij={"task_0": 0.4, "task_1": 0.6, "task_2": 0.5},
    P_ij={"task_0": 0.3, "task_1": 0.4, "task_2": 0.2},
    Task_ij={"task_0": 0.9, "task_1": 0.7, "task_2": 0.8}
)
```

### 3. Results Format
```python
results = {
    client_id: {
        'round_time': 8.0,           # Simulation time (seconds)
        'throughput': 15.2,          # Throughput (Mbps)
        'latency': 12.5,             # Average latency (ms)
        'packet_loss': 0.02,         # Packet loss ratio
        'task_metrics': {            # Task-specific metrics
            'task_0': 0.85,
            'task_1': 0.72,
            'task_2': 0.91
        }
    }
}
```

## Integration with MAPPO

### 1. In Multi-Task Server (`multi_task_server.py`)
```python
# Replace NS3 simulation call
network_metrics = self._simulate_model_transmission_with_ns3_multi_task(
    selected_clients, resource_allocations, num_tasks=len(self.mt_config.tasks)
)
```

### 2. Resource Allocation Conversion
```python
def convert_to_ns3_format(resource_allocations):
    ns3_allocations = {}
    for client_id, allocation in resource_allocations.items():
        ns3_allocations[client_id] = ResourceAllocation(
            select_ij=allocation.select_ij,
            q_ij=allocation.q_ij,
            B_ij=allocation.B_ij,
            P_ij=allocation.P_ij,
            Task_ij=allocation.Task_ij
        )
    return ns3_allocations
```

## Testing

### 1. Test Interface
```bash
# Start NS3 simulation
./waf --run wifi_exp_multi_fl

# In another terminal, run Python test
python test_ns3_multi_task_interface.py
```

### 2. Expected Output
```
2024-XX-XX - INFO - Connected to NS3 simulation at localhost:8080
2024-XX-XX - INFO - Running simulation...
2024-XX-XX - INFO - Client 0: Throughput: 15.2 Mbps, Latency: 12.5 ms
2024-XX-XX - INFO - Test completed successfully!
```

## Network Simulation Details

### 1. WiFi Configuration
- **Channel**: Default Yans WiFi channel
- **MAC**: 802.11 with AARF rate adaptation
- **Topology**: Star topology (AP at center, clients distributed)
- **Power Control**: Dynamic based on `P_ij` allocation

### 2. Traffic Simulation
- **Application**: UDP echo client/server
- **Packet Size**: Adaptive based on `B_ij` allocation
- **Traffic Rate**: Configurable data rate
- **QoS**: Priority-based on `Task_ij` values

### 3. Metrics Collection
- **Flow Monitor**: Detailed per-flow statistics
- **Network Metrics**: Throughput, latency, packet loss
- **Task Metrics**: Performance based on resource allocation

## Troubleshooting

### 1. Connection Issues
- Ensure NS3 simulation is running before Python connection
- Check port 8080 is not blocked by firewall
- Verify socket communication protocol matches

### 2. Build Issues
- Ensure NS3 is properly configured
- Check all header files are included
- Verify C++ compiler supports C++11 or later

### 3. Performance Issues
- Reduce number of clients for faster simulation
- Adjust simulation time in experiment code
- Use ethernet instead of wifi for simpler testing

## Future Enhancements

1. **Advanced QoS**: Implement traffic classes and priority queues
2. **Mobility Support**: Add client mobility models
3. **Energy Modeling**: Include energy consumption based on power allocation
4. **Security**: Add authentication and encryption simulation
5. **Scalability**: Support for larger numbers of clients and tasks
