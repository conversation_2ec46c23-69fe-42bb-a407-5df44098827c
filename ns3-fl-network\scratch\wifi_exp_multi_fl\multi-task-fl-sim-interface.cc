/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-task-fl-sim-interface.h"

namespace ns3 {

    NS_LOG_COMPONENT_DEFINE("MultiTaskFLSimProvider");

    void MultiTaskFLSimProvider::waitForConnection() {
        m_server_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (m_server_fd < 0) {
            NS_LOG_UNCOND("Could not create a socket");
            exit(-1);
        }
        int opt = 1;
        setsockopt(m_server_fd, SOL_SOCKET, SO_REUSEADDR | SO_REUSEPORT,
                   &opt, sizeof(opt));

        m_address.sin_family = AF_INET;
        m_address.sin_addr.s_addr = INADDR_ANY;
        m_address.sin_port = htons(m_port);

        if (bind(m_server_fd, (struct sockaddr *) &m_address, sizeof(m_address)) == -1) {
            NS_LOG_UNCOND("Could not bind to port " << m_port);
            exit(-1);
        }
        listen(m_server_fd, 3);

        NS_LOG_UNCOND("Multi-Task FL Simulation waiting for connection on port " << m_port);

        int addrlen = sizeof(m_address);
        m_new_socket = accept(m_server_fd, (struct sockaddr *) &m_address,
                            (socklen_t * ) & addrlen);

        if (m_new_socket < 0) {
            NS_LOG_UNCOND("Failed to accept connection");
            exit(-1);
        }

        NS_LOG_UNCOND("Multi-Task FL Simulation connected successfully");
    }

    MultiTaskFLSimProvider::COMMAND::Type MultiTaskFLSimProvider::recv(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {
        
        COMMAND c;
        int len = read(m_new_socket, (char *) &c, sizeof(c));

        if (len != sizeof(COMMAND)) {
            if (len != 0) {
                NS_LOG_UNCOND("Invalid Command: Len(" << len << ")!=(" << sizeof(COMMAND) << ")");
            } else {
                NS_LOG_UNCOND("Socket closed by Python");
            }
            close(m_new_socket);
            return COMMAND::Type::EXIT;
        }

        if (c.command == COMMAND::Type::EXIT) {
            NS_LOG_UNCOND("Exit Called");
            close(m_new_socket);
            return COMMAND::Type::EXIT;
        } else if (c.command != COMMAND::Type::RUN_SIMULATION) {
            NS_LOG_UNCOND("Invalid command");
            close(m_new_socket);
            return COMMAND::Type::EXIT;
        }

        NS_LOG_UNCOND("Received RUN_SIMULATION command for " << c.nClients << " clients, " << c.nTasks << " tasks");

        // Receive resource allocations for each client
        for (uint32_t i = 0; i < c.nClients; i++) {
            uint32_t clientId;
            if (sizeof(clientId) != read(m_new_socket, (char *) &clientId, sizeof(clientId))) {
                NS_LOG_UNCOND("Failed to read client ID");
                return COMMAND::Type::EXIT;
            }

            // Find the client session
            auto it = clientSessions.find(clientId);
            if (it == clientSessions.end()) {
                NS_LOG_UNCOND("Client " << clientId << " not found in session map");
                return COMMAND::Type::EXIT;
            }

            // Receive resource allocation for this client
            ResourceAllocationData allocationData = receiveResourceAllocation(clientId, c.nTasks);
            
            // Apply resource allocation to client session
            applyResourceAllocation(it->second, allocationData);

            NS_LOG_DEBUG("Applied resource allocation for client " << clientId);
        }

        return c.command;
    }

    MultiTaskFLSimProvider::ResourceAllocationData MultiTaskFLSimProvider::receiveResourceAllocation(
        uint32_t clientId, uint32_t numTasks) {
        
        ResourceAllocationData data;
        data.clientId = clientId;
        data.numTasks = numTasks;
        
        // Each task has 5 allocation values: select_ij, q_ij, B_ij, P_ij, Task_ij
        uint32_t totalValues = numTasks * 5;
        data.allocations.resize(totalValues);

        for (uint32_t i = 0; i < totalValues; i++) {
            double value;
            if (sizeof(value) != read(m_new_socket, (char *) &value, sizeof(value))) {
                NS_LOG_UNCOND("Failed to read allocation value " << i << " for client " << clientId);
                // Return empty data on error
                data.allocations.clear();
                return data;
            }
            data.allocations[i] = value;
        }

        NS_LOG_DEBUG("Received " << totalValues << " allocation values for client " << clientId);
        return data;
    }

    void MultiTaskFLSimProvider::applyResourceAllocation(
        std::shared_ptr<MultiTaskClientSession> clientSession,
        const ResourceAllocationData& allocationData) {
        
        ResourceAllocation allocation = convertToResourceAllocation(allocationData);
        clientSession->SetResourceAllocation(allocation);

        // Set client participation based on average selection probability
        double avgSelection = 0.0;
        for (const auto& pair : allocation.select_ij) {
            avgSelection += pair.second;
        }
        avgSelection /= allocation.select_ij.size();

        // Use threshold to determine participation
        bool inRound = avgSelection >= 0.5;
        clientSession->SetInRound(inRound);

        NS_LOG_DEBUG("Client " << clientSession->GetClientId() << 
                    " set to " << (inRound ? "participate" : "not participate") << 
                    " (avg selection: " << avgSelection << ")");
    }

    ResourceAllocation MultiTaskFLSimProvider::convertToResourceAllocation(
        const ResourceAllocationData& allocationData) {
        
        ResourceAllocation allocation;
        
        uint32_t idx = 0;
        for (uint32_t taskId = 0; taskId < allocationData.numTasks; taskId++) {
            std::string taskName = "task_" + std::to_string(taskId);
            
            // Extract 5 values per task: select_ij, q_ij, B_ij, P_ij, Task_ij
            allocation.select_ij[taskName] = allocationData.allocations[idx++];
            allocation.q_ij[taskName] = allocationData.allocations[idx++];
            allocation.B_ij[taskName] = allocationData.allocations[idx++];
            allocation.P_ij[taskName] = allocationData.allocations[idx++];
            allocation.Task_ij[taskName] = allocationData.allocations[idx++];
        }
        
        return allocation;
    }

    void MultiTaskFLSimProvider::send(std::map<int, Message> &roundResultMap) {
        uint32_t nResults = roundResultMap.size();
        
        // Send number of results
        if (write(m_new_socket, (char *) &nResults, sizeof(nResults)) != sizeof(nResults)) {
            NS_LOG_UNCOND("Failed to send number of results");
            return;
        }

        // Send each result
        for (const auto& pair : roundResultMap) {
            uint32_t clientId = pair.first;
            const Message& msg = pair.second;

            // Send client ID
            if (write(m_new_socket, (char *) &clientId, sizeof(clientId)) != sizeof(clientId)) {
                NS_LOG_UNCOND("Failed to send client ID");
                return;
            }

            // Send basic metrics
            if (write(m_new_socket, (char *) &msg.id, sizeof(msg.id)) != sizeof(msg.id) ||
                write(m_new_socket, (char *) &msg.roundTime, sizeof(msg.roundTime)) != sizeof(msg.roundTime) ||
                write(m_new_socket, (char *) &msg.throughput, sizeof(msg.throughput)) != sizeof(msg.throughput) ||
                write(m_new_socket, (char *) &msg.latency, sizeof(msg.latency)) != sizeof(msg.latency) ||
                write(m_new_socket, (char *) &msg.packetLoss, sizeof(msg.packetLoss)) != sizeof(msg.packetLoss)) {
                NS_LOG_UNCOND("Failed to send basic metrics for client " << clientId);
                return;
            }

            // Send task-specific metrics
            uint32_t nTaskMetrics = msg.taskMetrics.size();
            if (write(m_new_socket, (char *) &nTaskMetrics, sizeof(nTaskMetrics)) != sizeof(nTaskMetrics)) {
                NS_LOG_UNCOND("Failed to send task metrics count for client " << clientId);
                return;
            }

            for (const auto& taskPair : msg.taskMetrics) {
                // Send task name length and name
                uint32_t nameLen = taskPair.first.length();
                if (write(m_new_socket, (char *) &nameLen, sizeof(nameLen)) != sizeof(nameLen) ||
                    write(m_new_socket, taskPair.first.c_str(), nameLen) != (int)nameLen) {
                    NS_LOG_UNCOND("Failed to send task name for client " << clientId);
                    return;
                }

                // Send task metric value
                if (write(m_new_socket, (char *) &taskPair.second, sizeof(taskPair.second)) != sizeof(taskPair.second)) {
                    NS_LOG_UNCOND("Failed to send task metric value for client " << clientId);
                    return;
                }
            }
        }

        NS_LOG_DEBUG("Sent results for " << nResults << " clients");
    }

    void MultiTaskFLSimProvider::end() {
        COMMAND c;
        c.command = COMMAND::Type::ENDSIM;
        c.nClients = 0;
        c.nTasks = 0;
        
        write(m_new_socket, (char *) &c, sizeof(c));
        NS_LOG_UNCOND("Sent ENDSIM command");
    }

    void MultiTaskFLSimProvider::Close() {
        if (m_new_socket >= 0) {
            close(m_new_socket);
        }
        if (m_server_fd >= 0) {
            close(m_server_fd);
        }
        NS_LOG_UNCOND("Multi-Task FL Simulation connection closed");
    }
}
