/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-fl-experiment.h"
#include "ns3/internet-stack-helper.h"
#include "ns3/flow-monitor-helper.h"
#include "ns3/applications-module.h"
#include "ns3/csma-helper.h"
#include "ns3/wifi-module.h"
#include "ns3/mobility-module.h"
#include "ns3/network-module.h"
#include "ns3/internet-module.h"

namespace ns3 {

    NS_LOG_COMPONENT_DEFINE("MultiTaskExperiment");

    MultiTaskExperiment::MultiTaskExperiment(int numClients, int numTasks, std::string &networkType,
                                           int maxPacketSize, double txGain, double modelSize,
                                           std::string &dataRate, bool bAsync,
                                           MultiTaskFLSimProvider *pflSymProvider, FILE *fp, int round)
        : m_numClients(numClients), m_numTasks(numTasks), m_networkType(networkType),
          m_maxPacketSize(maxPacketSize), m_txGain(txGain), m_modelSize(modelSize),
          m_dataRate(dataRate), m_bAsync(bAsync), m_flSimProvider(pflSymProvider),
          m_fp(fp), m_round(round) {
        
        // Set base transmission power and bandwidth
        m_baseTxPower = 20.0; // dBm
        m_maxBandwidth = 100.0; // Mbps
        
        NS_LOG_INFO("Created MultiTaskExperiment for round " << round << 
                   " with " << numClients << " clients and " << numTasks << " tasks");
    }

    std::map<int, MultiTaskFLSimProvider::Message>
    MultiTaskExperiment::RunMultiTaskNetwork(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions,
        ns3::Time &timeOffset) {

        NS_LOG_INFO("Starting multi-task network simulation for round " << m_round);

        // Log resource allocations
        LogResourceAllocations(clientSessions);

        // Create nodes
        NodeContainer nodes;
        nodes.Create(m_numClients + 1); // +1 for server

        // Set up network based on type
        NetDeviceContainer devices;
        if (m_networkType == "wifi") {
            devices = SetupWifiNetwork(nodes, clientSessions);
        } else {
            devices = SetupEthernetNetwork(nodes, clientSessions);
        }

        // Install internet stack
        InternetStackHelper stack;
        stack.Install(nodes);

        // Assign IP addresses
        Ipv4AddressHelper address;
        address.SetBase("********", "*************");
        Ipv4InterfaceContainer interfaces = address.Assign(devices);

        // Apply task priority QoS
        ApplyTaskPriorityQoS(clientSessions);

        // Set up applications (simplified for multi-task FL)
        uint16_t port = 9;
        
        // Server application
        UdpEchoServerHelper echoServer(port);
        ApplicationContainer serverApps = echoServer.Install(nodes.Get(m_numClients)); // Server is last node
        serverApps.Start(Seconds(1.0));
        serverApps.Stop(Seconds(10.0));

        // Client applications with resource-aware configuration
        ApplicationContainer clientApps;
        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];
            if (clientSession && clientSession->GetInRound()) {
                
                // Configure client based on resource allocation
                UdpEchoClientHelper echoClient(interfaces.GetAddress(m_numClients), port);
                
                // Adjust packet size based on bandwidth allocation
                double avgBandwidth = 0.0;
                const auto& allocation = clientSession->GetResourceAllocation();
                for (const auto& pair : allocation.B_ij) {
                    avgBandwidth += pair.second;
                }
                avgBandwidth /= allocation.B_ij.size();
                
                uint32_t packetSize = std::min(m_maxPacketSize, 
                                             (uint32_t)(m_maxPacketSize * avgBandwidth));
                echoClient.SetAttribute("MaxPackets", UintegerValue(100));
                echoClient.SetAttribute("Interval", TimeValue(Seconds(0.1)));
                echoClient.SetAttribute("PacketSize", UintegerValue(packetSize));

                ApplicationContainer app = echoClient.Install(nodes.Get(i));
                app.Start(Seconds(2.0));
                app.Stop(Seconds(9.0));
                clientApps.Add(app);

                NS_LOG_DEBUG("Configured client " << i << " with packet size " << packetSize << 
                           " (bandwidth factor: " << avgBandwidth << ")");
            }
        }

        // Install flow monitor
        FlowMonitorHelper flowmon;
        Ptr<FlowMonitor> monitor = flowmon.InstallAll();

        // Run simulation
        Simulator::Stop(Seconds(10.0));
        Simulator::Run();

        // Collect results
        std::map<int, MultiTaskFLSimProvider::Message> results;
        
        monitor->CheckForLostPackets();
        Ptr<Ipv4FlowClassifier> classifier = DynamicCast<Ipv4FlowClassifier>(flowmon.GetClassifier());
        std::map<FlowId, FlowMonitor::FlowStats> stats = monitor->GetFlowStats();

        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];
            if (clientSession && clientSession->GetInRound()) {
                
                MultiTaskFLSimProvider::Message msg;
                msg.id = i;
                msg.roundTime = 8.0; // Simulation time
                msg.throughput = 0.0;
                msg.latency = 0.0;
                msg.packetLoss = 0.0;

                // Find flow stats for this client
                for (auto& flowPair : stats) {
                    Ipv4FlowClassifier::FiveTuple t = classifier->FindFlow(flowPair.first);
                    if (t.sourceAddress == interfaces.GetAddress(i)) {
                        const FlowMonitor::FlowStats& flowStats = flowPair.second;
                        
                        // Calculate basic metrics
                        msg.throughput = flowStats.rxBytes * 8.0 / flowStats.timeLastRxPacket.GetSeconds() / 1000000.0; // Mbps
                        msg.latency = flowStats.delaySum.GetMilliSeconds() / flowStats.rxPackets; // ms
                        msg.packetLoss = (double)(flowStats.txPackets - flowStats.rxPackets) / flowStats.txPackets;
                        
                        // Calculate task-specific metrics
                        msg.taskMetrics = CalculateTaskMetrics(clientSession, &flowStats);
                        break;
                    }
                }

                results[i] = msg;
                
                NS_LOG_INFO("Client " << i << " results: throughput=" << msg.throughput << 
                           " Mbps, latency=" << msg.latency << " ms, loss=" << msg.packetLoss);
            }
        }

        Simulator::Destroy();
        
        NS_LOG_INFO("Multi-task network simulation completed for round " << m_round);
        return results;
    }

    void MultiTaskExperiment::SetPosition(Ptr<Node> node, double radius, double theta) {
        Ptr<MobilityModel> mobility = node->GetObject<MobilityModel>();
        Vector pos(radius * cos(theta), radius * sin(theta), 0);
        mobility->SetPosition(pos);
    }

    Vector MultiTaskExperiment::GetPosition(Ptr<Node> node) {
        Ptr<MobilityModel> mobility = node->GetObject<MobilityModel>();
        return mobility->GetPosition();
    }

    void MultiTaskExperiment::LogResourceAllocations(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {
        
        NS_LOG_INFO("=== Resource Allocations for Round " << m_round << " ===");
        
        for (const auto& pair : clientSessions) {
            int clientId = pair.first;
            auto client = pair.second;
            
            if (client->GetInRound()) {
                const auto& allocation = client->GetResourceAllocation();
                
                NS_LOG_INFO("Client " << clientId << " (participating):");
                NS_LOG_INFO("  Total Power: " << client->GetTotalPowerAllocation());
                NS_LOG_INFO("  Avg Selection: " << client->GetAverageSelectionProbability());
                
                for (int taskId = 0; taskId < m_numTasks; taskId++) {
                    std::string taskName = "task_" + std::to_string(taskId);
                    NS_LOG_INFO("  " << taskName << ": select=" << 
                               client->GetTaskSelectionProbability(taskName) <<
                               ", compute=" << client->GetTaskComputeAllocation(taskName) <<
                               ", bandwidth=" << client->GetTaskBandwidthAllocation(taskName) <<
                               ", power=" << client->GetTaskPowerAllocation(taskName) <<
                               ", priority=" << client->GetTaskPriority(taskName));
                }
            } else {
                NS_LOG_INFO("Client " << clientId << " (not participating)");
            }
        }
        
        NS_LOG_INFO("=== End Resource Allocations ===");
    }

    NetDeviceContainer MultiTaskExperiment::SetupWifiNetwork(
        ns3::NodeContainer &nodes,
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {

        NS_LOG_INFO("Setting up WiFi network with multi-task resource allocation");

        // Create WiFi channel
        YansWifiChannelHelper channel = YansWifiChannelHelper::Default();
        YansWifiPhyHelper phy = YansWifiPhyHelper::Default();
        phy.SetChannel(channel.Create());

        // Configure WiFi MAC
        WifiHelper wifi;
        wifi.SetRemoteStationManager("ns3::AarfWifiManager");

        WifiMacHelper mac;
        Ssid ssid = Ssid("multi-task-fl-network");

        // Configure server (AP)
        mac.SetType("ns3::ApWifiMac", "Ssid", SsidValue(ssid));
        NetDeviceContainer apDevice = wifi.Install(phy, mac, nodes.Get(m_numClients));

        // Configure clients (STAs) with resource-aware settings
        mac.SetType("ns3::StaWifiMac", "Ssid", SsidValue(ssid), "ActiveProbing", BooleanValue(false));
        NetDeviceContainer staDevices;

        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];

            // Configure transmission power based on resource allocation
            double totalPower = clientSession->GetTotalPowerAllocation();
            double txPower = m_baseTxPower + m_txGain + (totalPower * 10.0); // Scale power allocation

            phy.Set("TxPowerStart", DoubleValue(txPower));
            phy.Set("TxPowerEnd", DoubleValue(txPower));

            NetDeviceContainer clientDevice = wifi.Install(phy, mac, nodes.Get(i));
            staDevices.Add(clientDevice);

            NS_LOG_DEBUG("Client " << i << " configured with TX power: " << txPower << " dBm");
        }

        // Set up mobility
        MobilityHelper mobility;

        // Server at center
        mobility.SetPositionAllocator("ns3::ListPositionAllocator");
        Ptr<ListPositionAllocator> serverPosAlloc = CreateObject<ListPositionAllocator>();
        serverPosAlloc->Add(Vector(0.0, 0.0, 0.0));
        mobility.SetPositionAllocator(serverPosAlloc);
        mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
        mobility.Install(nodes.Get(m_numClients));

        // Clients at specified positions
        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];
            SetPosition(nodes.Get(i), clientSession->GetRadius(), clientSession->GetTheta());
        }

        // Combine all devices
        NetDeviceContainer allDevices;
        allDevices.Add(staDevices);
        allDevices.Add(apDevice);

        return allDevices;
    }

    NetDeviceContainer MultiTaskExperiment::SetupEthernetNetwork(
        NodeContainer &nodes,
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {

        NS_LOG_INFO("Setting up Ethernet network");

        CsmaHelper csma;
        csma.SetChannelAttribute("DataRate", StringValue("100Mbps"));
        csma.SetChannelAttribute("Delay", TimeValue(NanoSeconds(6560)));

        NetDeviceContainer devices = csma.Install(nodes);
        return devices;
    }

    void MultiTaskExperiment::ConfigureTransmissionPower(Ptr<NetDevice> device, double powerAllocation) {
        // Configure transmission power based on allocation
        // This is a simplified implementation
        NS_LOG_DEBUG("Configuring transmission power with allocation: " << powerAllocation);
    }

    void MultiTaskExperiment::ConfigureBandwidth(Ptr<NetDevice> device, double bandwidthAllocation) {
        // Configure bandwidth based on allocation
        // This is a simplified implementation
        NS_LOG_DEBUG("Configuring bandwidth with allocation: " << bandwidthAllocation);
    }

    std::map<std::string, double> MultiTaskExperiment::CalculateTaskMetrics(
        std::shared_ptr<MultiTaskClientSession> clientSession,
        const Ptr<FlowMonitor::FlowStats> flowStats) {

        std::map<std::string, double> taskMetrics;
        const auto& allocation = clientSession->GetResourceAllocation();

        // Calculate task-specific performance metrics based on resource allocation
        for (const auto& pair : allocation.Task_ij) {
            const std::string& taskName = pair.first;
            double priority = pair.second;
            double computeAlloc = clientSession->GetTaskComputeAllocation(taskName);
            double bandwidthAlloc = clientSession->GetTaskBandwidthAllocation(taskName);

            // Simulate task performance based on resource allocation
            double taskPerformance = (priority * 0.3 + computeAlloc * 0.4 + bandwidthAlloc * 0.3);
            taskMetrics[taskName] = taskPerformance;
        }

        return taskMetrics;
    }

    void MultiTaskExperiment::ApplyTaskPriorityQoS(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {

        NS_LOG_INFO("Applying task priority-based QoS");

        // This is a simplified QoS implementation
        // In a real implementation, you would configure traffic classes,
        // priority queues, and bandwidth allocation based on task priorities

        for (const auto& pair : clientSessions) {
            auto client = pair.second;
            if (client->GetInRound()) {
                const auto& allocation = client->GetResourceAllocation();

                // Find highest priority task
                double maxPriority = 0.0;
                std::string highestPriorityTask;
                for (const auto& taskPair : allocation.Task_ij) {
                    if (taskPair.second > maxPriority) {
                        maxPriority = taskPair.second;
                        highestPriorityTask = taskPair.first;
                    }
                }

                NS_LOG_DEBUG("Client " << client->GetClientId() <<
                           " highest priority task: " << highestPriorityTask <<
                           " (priority: " << maxPriority << ")");
            }
        }
    }
}
