/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-fl-experiment.h"
#include "ns3/internet-stack-helper.h"
#include "ns3/flow-monitor-helper.h"
#include "ns3/applications-module.h"
#include "ns3/csma-helper.h"
#include "ns3/wifi-module.h"
#include "ns3/mobility-module.h"
#include "ns3/network-module.h"
#include "ns3/internet-module.h"
#include "ns3/traffic-control-module.h"
#include "ns3/queue-size.h"
#include <algorithm>
#include <functional>

namespace ns3 {

    NS_LOG_COMPONENT_DEFINE("MultiTaskExperiment");

    MultiTaskExperiment::MultiTaskExperiment(int numClients, int numTasks, std::string &networkType,
                                           int maxPacketSize, double txGain, double modelSize,
                                           std::string &dataRate, bool bAsync,
                                           MultiTaskFLSimProvider *pflSymProvider, FILE *fp, int round)
        : m_numClients(numClients), m_numTasks(numTasks), m_networkType(networkType),
          m_maxPacketSize(maxPacketSize), m_txGain(txGain), m_modelSize(modelSize),
          m_dataRate(dataRate), m_bAsync(bAsync), m_flSimProvider(pflSymProvider),
          m_fp(fp), m_round(round) {
        
        // Set base transmission power and bandwidth
        m_baseTxPower = 20.0; // dBm
        m_maxBandwidth = 100.0; // Mbps
        
        NS_LOG_INFO("Created MultiTaskExperiment for round " << round << 
                   " with " << numClients << " clients and " << numTasks << " tasks");
    }

    std::map<int, MultiTaskFLSimProvider::Message>
    MultiTaskExperiment::RunMultiTaskNetwork(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions,
        ns3::Time &timeOffset) {

        NS_LOG_INFO("Starting multi-task network simulation for round " << m_round);

        // Log resource allocations
        LogResourceAllocations(clientSessions);

        // Create nodes
        NodeContainer nodes;
        nodes.Create(m_numClients + 1); // +1 for server

        // Set up network based on type
        NetDeviceContainer devices;
        if (m_networkType == "wifi") {
            devices = SetupWifiNetwork(nodes, clientSessions);
        } else {
            devices = SetupEthernetNetwork(nodes, clientSessions);
        }

        // Install internet stack
        InternetStackHelper stack;
        stack.Install(nodes);

        // Assign IP addresses
        Ipv4AddressHelper address;
        address.SetBase("********", "*************");
        Ipv4InterfaceContainer interfaces = address.Assign(devices);

        // Apply task priority QoS
        ApplyTaskPriorityQoS(clientSessions);

        // Set up applications (simplified for multi-task FL)
        uint16_t port = 9;
        
        // Server application
        UdpEchoServerHelper echoServer(port);
        ApplicationContainer serverApps = echoServer.Install(nodes.Get(m_numClients)); // Server is last node
        serverApps.Start(Seconds(1.0));
        serverApps.Stop(Seconds(10.0));

        // Client applications with comprehensive resource-aware configuration
        ApplicationContainer clientApps;
        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];
            if (clientSession && clientSession->GetInRound()) {

                const auto& allocation = clientSession->GetResourceAllocation();

                // Configure multiple applications per client based on tasks
                for (const auto& taskPair : allocation.select_ij) {
                    const std::string& taskName = taskPair.first;
                    double taskSelection = taskPair.second;

                    // Only create application if task is selected (threshold-based)
                    if (taskSelection >= 0.3) {  // Task selection threshold

                        // Get task-specific allocations
                        double taskBandwidth = allocation.B_ij.at(taskName);
                        double taskCompute = allocation.q_ij.at(taskName);
                        double taskPriority = allocation.Task_ij.at(taskName);

                        // Configure task-specific application
                        uint16_t taskPort = port + std::hash<std::string>{}(taskName) % 1000;
                        UdpEchoClientHelper echoClient(interfaces.GetAddress(m_numClients), taskPort);

                        // Bandwidth allocation: Adjust packet size and interval
                        uint32_t packetSize = std::max(64u,
                            std::min(m_maxPacketSize, (uint32_t)(m_maxPacketSize * taskBandwidth)));

                        // Compute allocation: Adjust transmission frequency (more compute = more frequent)
                        double baseInterval = 0.1; // seconds
                        double computeInterval = baseInterval / std::max(0.1, taskCompute);

                        // Priority allocation: Adjust number of packets (higher priority = more packets)
                        uint32_t maxPackets = std::max(10u, (uint32_t)(100 * taskPriority));

                        echoClient.SetAttribute("MaxPackets", UintegerValue(maxPackets));
                        echoClient.SetAttribute("Interval", TimeValue(Seconds(computeInterval)));
                        echoClient.SetAttribute("PacketSize", UintegerValue(packetSize));

                        // Set Type of Service (ToS) based on task priority for QoS
                        uint8_t tos = (uint8_t)(taskPriority * 255);
                        echoClient.SetAttribute("Tos", UintegerValue(tos));

                        ApplicationContainer taskApp = echoClient.Install(nodes.Get(i));

                        // Stagger start times based on task priority (higher priority starts earlier)
                        double startTime = 2.0 + (1.0 - taskPriority) * 0.5;
                        taskApp.Start(Seconds(startTime));
                        taskApp.Stop(Seconds(9.0));
                        clientApps.Add(taskApp);

                        NS_LOG_INFO("Client " << i << " Task " << taskName << ": "
                                   << "packetSize=" << packetSize
                                   << ", interval=" << computeInterval << "s"
                                   << ", maxPackets=" << maxPackets
                                   << ", priority=" << taskPriority
                                   << ", tos=" << (int)tos);
                    }
                }
            }
        }

        // Install flow monitor
        FlowMonitorHelper flowmon;
        Ptr<FlowMonitor> monitor = flowmon.InstallAll();

        // Run simulation
        Simulator::Stop(Seconds(10.0));
        Simulator::Run();

        // Collect results
        std::map<int, MultiTaskFLSimProvider::Message> results;
        
        monitor->CheckForLostPackets();
        Ptr<Ipv4FlowClassifier> classifier = DynamicCast<Ipv4FlowClassifier>(flowmon.GetClassifier());
        std::map<FlowId, FlowMonitor::FlowStats> stats = monitor->GetFlowStats();

        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];
            if (clientSession && clientSession->GetInRound()) {
                
                MultiTaskFLSimProvider::Message msg;
                msg.id = i;
                msg.roundTime = 8.0; // Simulation time
                msg.throughput = 0.0;
                msg.latency = 0.0;
                msg.packetLoss = 0.0;

                // Find flow stats for this client
                for (auto& flowPair : stats) {
                    Ipv4FlowClassifier::FiveTuple t = classifier->FindFlow(flowPair.first);
                    if (t.sourceAddress == interfaces.GetAddress(i)) {
                        const FlowMonitor::FlowStats& flowStats = flowPair.second;
                        
                        // Calculate basic metrics
                        msg.throughput = flowStats.rxBytes * 8.0 / flowStats.timeLastRxPacket.GetSeconds() / 1000000.0; // Mbps
                        msg.latency = flowStats.delaySum.GetMilliSeconds() / flowStats.rxPackets; // ms
                        msg.packetLoss = (double)(flowStats.txPackets - flowStats.rxPackets) / flowStats.txPackets;
                        
                        // Calculate task-specific metrics
                        msg.taskMetrics = CalculateTaskMetrics(clientSession, &flowStats);
                        break;
                    }
                }

                results[i] = msg;
                
                NS_LOG_INFO("Client " << i << " results: throughput=" << msg.throughput << 
                           " Mbps, latency=" << msg.latency << " ms, loss=" << msg.packetLoss);
            }
        }

        Simulator::Destroy();
        
        NS_LOG_INFO("Multi-task network simulation completed for round " << m_round);
        return results;
    }

    void MultiTaskExperiment::SetPosition(Ptr<Node> node, double radius, double theta) {
        Ptr<MobilityModel> mobility = node->GetObject<MobilityModel>();
        Vector pos(radius * cos(theta), radius * sin(theta), 0);
        mobility->SetPosition(pos);
    }

    Vector MultiTaskExperiment::GetPosition(Ptr<Node> node) {
        Ptr<MobilityModel> mobility = node->GetObject<MobilityModel>();
        return mobility->GetPosition();
    }

    void MultiTaskExperiment::LogResourceAllocations(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {
        
        NS_LOG_INFO("=== Resource Allocations for Round " << m_round << " ===");
        
        for (const auto& pair : clientSessions) {
            int clientId = pair.first;
            auto client = pair.second;
            
            if (client->GetInRound()) {
                const auto& allocation = client->GetResourceAllocation();
                
                NS_LOG_INFO("Client " << clientId << " (participating):");
                NS_LOG_INFO("  Total Power: " << client->GetTotalPowerAllocation());
                NS_LOG_INFO("  Avg Selection: " << client->GetAverageSelectionProbability());
                
                for (int taskId = 0; taskId < m_numTasks; taskId++) {
                    std::string taskName = "task_" + std::to_string(taskId);
                    NS_LOG_INFO("  " << taskName << ": select=" << 
                               client->GetTaskSelectionProbability(taskName) <<
                               ", compute=" << client->GetTaskComputeAllocation(taskName) <<
                               ", bandwidth=" << client->GetTaskBandwidthAllocation(taskName) <<
                               ", power=" << client->GetTaskPowerAllocation(taskName) <<
                               ", priority=" << client->GetTaskPriority(taskName));
                }
            } else {
                NS_LOG_INFO("Client " << clientId << " (not participating)");
            }
        }
        
        NS_LOG_INFO("=== End Resource Allocations ===");
    }

    NetDeviceContainer MultiTaskExperiment::SetupWifiNetwork(
        ns3::NodeContainer &nodes,
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {

        NS_LOG_INFO("Setting up WiFi network with multi-task resource allocation");

        // Create WiFi channel
        YansWifiChannelHelper channel = YansWifiChannelHelper::Default();
        YansWifiPhyHelper phy = YansWifiPhyHelper::Default();
        phy.SetChannel(channel.Create());

        // Configure WiFi MAC
        WifiHelper wifi;
        wifi.SetRemoteStationManager("ns3::AarfWifiManager");

        WifiMacHelper mac;
        Ssid ssid = Ssid("multi-task-fl-network");

        // Configure server (AP)
        mac.SetType("ns3::ApWifiMac", "Ssid", SsidValue(ssid));
        NetDeviceContainer apDevice = wifi.Install(phy, mac, nodes.Get(m_numClients));

        // Configure clients (STAs) with resource-aware settings
        mac.SetType("ns3::StaWifiMac", "Ssid", SsidValue(ssid), "ActiveProbing", BooleanValue(false));
        NetDeviceContainer staDevices;

        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];

            if (clientSession->GetInRound()) {
                // Configure transmission power based on resource allocation
                double totalPower = clientSession->GetTotalPowerAllocation();

                // Find maximum task priority to boost power for high-priority tasks
                double maxTaskPriority = 0.0;
                const auto& allocation = clientSession->GetResourceAllocation();
                for (const auto& pair : allocation.Task_ij) {
                    maxTaskPriority = std::max(maxTaskPriority, pair.second);
                }

                // Calculate adaptive power: base + gain + power_allocation + priority_boost
                double priorityBoost = maxTaskPriority * 5.0; // Up to 5dB boost for high priority
                double txPower = m_baseTxPower + m_txGain + (totalPower * 10.0) + priorityBoost;

                // Ensure power is within reasonable bounds
                txPower = std::max(0.0, std::min(30.0, txPower)); // 0-30 dBm range

                phy.Set("TxPowerStart", DoubleValue(txPower));
                phy.Set("TxPowerEnd", DoubleValue(txPower));

                NS_LOG_INFO("Client " << i << " configured with adaptive TX power: " << txPower
                           << " dBm (base=" << m_baseTxPower << ", gain=" << m_txGain
                           << ", power_alloc=" << totalPower << ", priority_boost=" << priorityBoost << ")");
            } else {
                // Non-participating clients get minimal power
                double minPower = m_baseTxPower + m_txGain;
                phy.Set("TxPowerStart", DoubleValue(minPower));
                phy.Set("TxPowerEnd", DoubleValue(minPower));

                NS_LOG_DEBUG("Client " << i << " (non-participating) configured with minimal TX power: " << minPower << " dBm");
            }

            NetDeviceContainer clientDevice = wifi.Install(phy, mac, nodes.Get(i));
            staDevices.Add(clientDevice);
        }

        // Set up mobility
        MobilityHelper mobility;

        // Server at center
        mobility.SetPositionAllocator("ns3::ListPositionAllocator");
        Ptr<ListPositionAllocator> serverPosAlloc = CreateObject<ListPositionAllocator>();
        serverPosAlloc->Add(Vector(0.0, 0.0, 0.0));
        mobility.SetPositionAllocator(serverPosAlloc);
        mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
        mobility.Install(nodes.Get(m_numClients));

        // Clients at specified positions
        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];
            SetPosition(nodes.Get(i), clientSession->GetRadius(), clientSession->GetTheta());
        }

        // Apply individual device configurations
        for (int i = 0; i < m_numClients; i++) {
            auto clientSession = clientSessions[i];
            if (clientSession->GetInRound()) {
                Ptr<NetDevice> clientDevice = staDevices.Get(i);

                // Configure bandwidth for each task
                const auto& allocation = clientSession->GetResourceAllocation();
                double avgBandwidth = 0.0;
                for (const auto& pair : allocation.B_ij) {
                    avgBandwidth += pair.second;
                }
                avgBandwidth /= allocation.B_ij.size();

                // Apply bandwidth configuration
                ConfigureBandwidth(clientDevice, avgBandwidth);

                // Power is already configured during device creation
                NS_LOG_DEBUG("Applied post-creation configuration for client " << i);
            }
        }

        // Combine all devices
        NetDeviceContainer allDevices;
        allDevices.Add(staDevices);
        allDevices.Add(apDevice);

        return allDevices;
    }

    NetDeviceContainer MultiTaskExperiment::SetupEthernetNetwork(
        NodeContainer &nodes,
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {

        NS_LOG_INFO("Setting up Ethernet network");

        CsmaHelper csma;
        csma.SetChannelAttribute("DataRate", StringValue("100Mbps"));
        csma.SetChannelAttribute("Delay", TimeValue(NanoSeconds(6560)));

        NetDeviceContainer devices = csma.Install(nodes);
        return devices;
    }

    void MultiTaskExperiment::ConfigureTransmissionPower(Ptr<NetDevice> device, double powerAllocation) {
        // Configure transmission power for WiFi devices
        if (device->GetInstanceTypeId().GetName() == "ns3::WifiNetDevice") {
            Ptr<WifiNetDevice> wifiDevice = DynamicCast<WifiNetDevice>(device);
            if (wifiDevice) {
                Ptr<WifiPhy> phy = wifiDevice->GetPhy();
                if (phy) {
                    // Calculate power based on allocation (0-1 maps to 0-30 dBm)
                    double txPower = powerAllocation * 30.0;
                    phy->SetTxPowerStart(txPower);
                    phy->SetTxPowerEnd(txPower);

                    NS_LOG_DEBUG("Configured device TX power: " << txPower << " dBm (allocation: " << powerAllocation << ")");
                }
            }
        }
    }

    void MultiTaskExperiment::ConfigureBandwidth(Ptr<NetDevice> device, double bandwidthAllocation) {
        // Configure bandwidth allocation through traffic control
        if (device->GetNode()) {
            Ptr<TrafficControlLayer> tc = device->GetNode()->GetObject<TrafficControlLayer>();
            if (tc) {
                // Get queue disc for this device
                Ptr<QueueDisc> queueDisc = tc->GetRootQueueDiscOnDevice(device);
                if (queueDisc) {
                    // Configure rate limiting based on bandwidth allocation
                    // This is a simplified approach - in practice you'd use token bucket filters

                    // Calculate max rate based on allocation (0-1 maps to 0-100 Mbps)
                    uint32_t maxRate = (uint32_t)(bandwidthAllocation * 100 * 1000000); // bps

                    // Set queue size based on bandwidth (more bandwidth = larger queue)
                    uint32_t queueSize = std::max(10u, (uint32_t)(bandwidthAllocation * 1000));
                    queueDisc->SetMaxSize(QueueSize(QueueSizeUnit::PACKETS, queueSize));

                    NS_LOG_DEBUG("Configured device bandwidth: max_rate=" << maxRate << " bps, queue_size=" << queueSize
                               << " packets (allocation: " << bandwidthAllocation << ")");
                }
            }
        }
    }

    std::map<std::string, double> MultiTaskExperiment::CalculateTaskMetrics(
        std::shared_ptr<MultiTaskClientSession> clientSession,
        const Ptr<FlowMonitor::FlowStats> flowStats) {

        std::map<std::string, double> taskMetrics;
        const auto& allocation = clientSession->GetResourceAllocation();

        // Calculate task-specific performance metrics based on resource allocation
        for (const auto& pair : allocation.Task_ij) {
            const std::string& taskName = pair.first;
            double priority = pair.second;
            double computeAlloc = clientSession->GetTaskComputeAllocation(taskName);
            double bandwidthAlloc = clientSession->GetTaskBandwidthAllocation(taskName);

            // Simulate task performance based on resource allocation
            double taskPerformance = (priority * 0.3 + computeAlloc * 0.4 + bandwidthAlloc * 0.3);
            taskMetrics[taskName] = taskPerformance;
        }

        return taskMetrics;
    }

    void MultiTaskExperiment::ApplyTaskPriorityQoS(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions) {

        NS_LOG_INFO("Applying comprehensive task priority-based QoS");

        // Configure traffic control and QoS based on task priorities
        for (const auto& pair : clientSessions) {
            auto client = pair.second;
            if (client->GetInRound()) {
                const auto& allocation = client->GetResourceAllocation();

                // Analyze task priorities for this client
                std::vector<std::pair<std::string, double>> taskPriorities;
                for (const auto& taskPair : allocation.Task_ij) {
                    taskPriorities.push_back({taskPair.first, taskPair.second});
                }

                // Sort tasks by priority (highest first)
                std::sort(taskPriorities.begin(), taskPriorities.end(),
                         [](const auto& a, const auto& b) { return a.second > b.second; });

                NS_LOG_INFO("Client " << client->GetClientId() << " task priority ranking:");
                for (size_t i = 0; i < taskPriorities.size(); i++) {
                    const auto& task = taskPriorities[i];

                    // Calculate QoS class based on priority ranking
                    uint8_t qosClass = 0; // Best effort
                    if (i == 0 && task.second > 0.8) {
                        qosClass = 3; // Voice (highest priority)
                    } else if (i <= 1 && task.second > 0.6) {
                        qosClass = 2; // Video (high priority)
                    } else if (task.second > 0.4) {
                        qosClass = 1; // Background (medium priority)
                    }

                    // Get corresponding bandwidth and compute allocations
                    double taskBandwidth = allocation.B_ij.at(task.first);
                    double taskCompute = allocation.q_ij.at(task.first);

                    NS_LOG_INFO("  " << task.first << ": priority=" << task.second
                               << ", qos_class=" << (int)qosClass
                               << ", bandwidth_alloc=" << taskBandwidth
                               << ", compute_alloc=" << taskCompute);

                    // Store QoS configuration for later use in traffic shaping
                    // In a full implementation, this would configure:
                    // - Traffic classes (AC_VO, AC_VI, AC_BE, AC_BK)
                    // - Queue disciplines (priority queues, weighted fair queuing)
                    // - Rate limiting based on bandwidth allocation
                    // - CPU scheduling based on compute allocation
                }

                // Calculate overall resource utilization efficiency
                double totalBandwidthUtil = 0.0;
                double totalComputeUtil = 0.0;
                double weightedPrioritySum = 0.0;

                for (const auto& taskPair : allocation.Task_ij) {
                    const std::string& taskName = taskPair.first;
                    double priority = taskPair.second;
                    double bandwidth = allocation.B_ij.at(taskName);
                    double compute = allocation.q_ij.at(taskName);

                    totalBandwidthUtil += bandwidth;
                    totalComputeUtil += compute;
                    weightedPrioritySum += priority * (bandwidth + compute) / 2.0;
                }

                NS_LOG_INFO("Client " << client->GetClientId() << " resource summary: "
                           << "total_bandwidth_util=" << totalBandwidthUtil
                           << ", total_compute_util=" << totalComputeUtil
                           << ", weighted_priority_score=" << weightedPrioritySum);
            }
        }
    }
}
