import numpy as np
from sklearn.cluster import KMeans


# 特征收集与子区域划分函数
def feature_collection_and_subarea_partition(X, K=10):
    """
    将特征空间划分为多个子区域，并返回每个区域的质心。
    :param X: 输入的特征矩阵，大小为 N x D
    :param K: 子区域数量
    :return: 子区域质心列表
    """
    # 使用KMeans对特征空间进行划分
    kmeans = KMeans(n_clusters=K, random_state=42)
    kmeans.fit(X)

    # 提取每个子区域的质心
    centroids = kmeans.cluster_centers_
    return centroids


# Grouping和Pruning过程
def grouping_and_pruning(region_centroid, all_centroids, L=5, alpha_range=(0, 1)):
    """
    对给定的区域质心进行分组，并通过凸组合生成子质心。
    :param region_centroid: 区域的质心
    :param all_centroids: 所有区域的质心
    :param L: 每个区域的最近邻数量
    :param alpha_range: 控制凸组合的范围
    :return: 子质心和最近邻质心
    """
    # 固定随机种子，确保每次选择相同的邻居
    np.random.seed(42)
    # 计算该区域质心的L个最近邻
    distances = np.linalg.norm(all_centroids - region_centroid, axis=1)
    nearest_neighbors_idx = np.argsort(distances)[:L]
    nearest_centroids = all_centroids[nearest_neighbors_idx]

    # 计算子质心
    subcentroids = []
    for neighbor in nearest_centroids:
        for alpha in np.linspace(alpha_range[0], alpha_range[1], num=10):  # 选取不同的alpha值
            subcentroid = region_centroid + alpha * (neighbor - region_centroid)
            subcentroids.append(subcentroid)

    return np.array(subcentroids), nearest_centroids


# 主函数：执行特征收集、子区域划分、分组与修剪
def grouping_and_pruning_process(X, K=10, L=5, alpha_range=(0, 1)):
    """
    执行特征收集与子区域划分，并进行Grouping和Pruning处理。
    :param X: 输入的特征矩阵，大小为 N x D
    :param K: 子区域数量
    :param L: 每个区域的最近邻数量
    :param alpha_range: 控制凸组合的范围
    :return: 子区域划分后的子质心列表，修剪后的最近邻质心
    """
    # 1. 特征收集与子区域划分
    centroids = feature_collection_and_subarea_partition(X, K)

    # 2. 对每个区域执行Group和Prune
    grouped_subcentroids = []
    pruned_subcentroids = []

    for c in centroids:
        subcentroids, nearest_centroids = grouping_and_pruning(c, centroids, L, alpha_range)
        grouped_subcentroids.append(subcentroids)
        pruned_subcentroids.append(nearest_centroids)

    return np.array(grouped_subcentroids), np.array(pruned_subcentroids)


if __name__ == "__main__":
    # 示例：执行主函数
    N = 1000  # 特征数量
    D = 128  # 每个特征的维度
    X = np.random.rand(N, D)  # 生成随机特征子集

    grouped_subcentroids, pruned_subcentroids = grouping_and_pruning_process(X, K=10, L=5, alpha_range=(0, 1))

    # 输出结果
    print("分组后的子质心：")
    print(grouped_subcentroids)  # 特征收集与子区域划分的最终输出
    print("grouped_subcentroids shape:", grouped_subcentroids.shape)
    # print("\n修剪后的最近邻质心：")
    # print(pruned_subcentroids)
