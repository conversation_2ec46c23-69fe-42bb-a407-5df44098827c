import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np

from coldstart import data_utils


# 定义 GRU 模型结构
class GRUModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim=1):
        super(GRUModel, self).__init__()
        self.gru = nn.GRU(input_size=input_dim, hidden_size=hidden_dim, batch_first=True)
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        out1, h_n = self.gru(x)
        out = self.fc(h_n)
        return out, out1  # 返回最后一层输出状态作为特征向量


# 转换为时间序列格式 (sequence_length, features)
def create_sequences(data, seq_length):
    windows = []
    labels = []
    for i in range(len(data) - seq_length):
        window = data[i:i + seq_length]
        label = data[i + seq_length, -1]  # 只预测目标变量（最后一个字段）
        windows.append(window)
        labels.append(label)
    return np.array(windows), np.array(labels)


def extract_gru_features(model, data_loader, device):
    features_arr = []
    with torch.no_grad():  # 禁用梯度计算，提高性能
        for images, _ in data_loader:
            # 通过模型提取特征
            images = images.to(device)
            _, features = model(images)
            features_arr.append(features.cpu().numpy())  # 将输出转为 numpy 数组并存储
            print('Extracted features shape:', features.shape)

    return np.concatenate(features_arr, axis=0)


def extract_text_data_set_name_features(data_name='airquality', retrain=True):
    assert 'airquality' in data_name.lower()

    current_file_path = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file_path)
    data_dir = os.path.join(current_dir, '../data/air_quality')
    train_set, test_set = data_utils.load_air_quality(data_dir)
    train_loader = DataLoader(train_set, batch_size=64, shuffle=False)
    # features_data_set
    d_indices = torch.arange(0, 1000)
    features_data_set = torch.utils.data.Subset(train_set, d_indices)
    features_data_loader = DataLoader(features_data_set, batch_size=64, shuffle=False)

    input_dim = 1
    hidden_dim = 1000

    MODEL_PATH = os.path.join(current_dir, "stage1_text_model/gru_air_quality_model.pth")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    if os.path.exists(MODEL_PATH) and not retrain:
        print("加载已存在的模型...")
        model = GRUModel(input_dim, hidden_dim, 1)
        model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
        model = model.to(device)
        model.eval()
    else:
        print("没有找到现有模型或需要重训练，开始训练...")
        model = GRUModel(input_dim=input_dim, hidden_dim=hidden_dim, output_dim=1).to(device)
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

        num_epochs = 300
        for epoch in range(num_epochs):
            model.train()
            total_loss = 0
            for x_batch, y_batch in train_loader:
                x_batch = x_batch.to(device)
                y_batch = y_batch.to(device)

                optimizer.zero_grad()
                outputs, _ = model(x_batch)
                loss = criterion(outputs, y_batch)
                loss.backward()
                optimizer.step()
                total_loss += loss.item()

            if (epoch + 1) % 10 == 0:
                print(f'Epoch [{epoch + 1}/{num_epochs}], Loss: {total_loss:.4f}')

        # 保存训练好的模型
        torch.save(model.state_dict(), MODEL_PATH)
        print(f"模型已保存到 {MODEL_PATH}")

    features = extract_gru_features(model, features_data_loader, device)
    return features


if __name__ == "__main__":
    mnist_features = extract_text_data_set_name_features(data_name='airquality')
    print("Extracted Text Features Shape:", mnist_features.shape)
