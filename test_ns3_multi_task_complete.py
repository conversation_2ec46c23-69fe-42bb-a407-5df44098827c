#!/usr/bin/env python3
"""
Complete test for NS3 multi-task federated learning simulation
Tests all ResourceAllocation actions: select_ij, q_ij, B_ij, P_ij, Task_ij
"""

import socket
import struct
import time
import numpy as np
import logging
from typing import Dict, List
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ResourceAllocation:
    """Resource allocation from MAPPO"""
    select_ij: Dict[str, float]  # Task -> Selection probability [0,1]
    q_ij: Dict[str, float]       # Task -> Computing resource allocation [0,1]
    B_ij: Dict[str, float]       # Task -> Bandwidth allocation [0,1]
    P_ij: Dict[str, float]       # Task -> Power allocation [0,1]
    Task_ij: Dict[str, float]    # Task -> Task priority [0,1]


class NS3MultiTaskTester:
    """Complete tester for NS3 multi-task FL simulation"""
    
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.socket = None
        
    def connect(self):
        """Connect to NS3 simulation"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            logger.info(f"Connected to NS3 simulation at {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to NS3 simulation: {e}")
            return False
    
    def send_resource_allocations(self, allocations: Dict[int, ResourceAllocation], n_tasks: int):
        """Send resource allocations to NS3"""
        n_clients = len(allocations)
        
        # Send command: RUN_SIMULATION (1), num_clients, num_tasks
        command_data = struct.pack('III', 1, n_clients, n_tasks)
        self.socket.send(command_data)
        
        # Send resource allocations for each client
        for client_id in sorted(allocations.keys()):
            allocation = allocations[client_id]
            
            # Send client ID
            self.socket.send(struct.pack('I', client_id))
            
            # Send allocation data (5 values per task: select_ij, q_ij, B_ij, P_ij, Task_ij)
            allocation_values = []
            for task_id in range(n_tasks):
                task_name = f"task_{task_id}"
                allocation_values.extend([
                    allocation.select_ij.get(task_name, 0.0),
                    allocation.q_ij.get(task_name, 0.0),
                    allocation.B_ij.get(task_name, 0.0),
                    allocation.P_ij.get(task_name, 0.0),
                    allocation.Task_ij.get(task_name, 0.0)
                ])
            
            # Send all values as doubles
            for value in allocation_values:
                self.socket.send(struct.pack('d', value))
            
            logger.debug(f"Sent resource allocation for client {client_id}")
    
    def receive_results(self) -> Dict[int, Dict]:
        """Receive simulation results from NS3"""
        # Receive number of results
        n_results_data = self.socket.recv(4)
        n_results = struct.unpack('I', n_results_data)[0]
        
        results = {}
        for _ in range(n_results):
            # Receive client ID
            client_id_data = self.socket.recv(4)
            client_id = struct.unpack('I', client_id_data)[0]
            
            # Receive basic metrics
            metrics_data = self.socket.recv(8 + 8 + 8 + 8 + 8)  # 5 doubles
            id_val, round_time, throughput, latency, packet_loss = struct.unpack('Qdddd', metrics_data)
            
            # Receive task-specific metrics
            n_task_metrics_data = self.socket.recv(4)
            n_task_metrics = struct.unpack('I', n_task_metrics_data)[0]
            
            task_metrics = {}
            for _ in range(n_task_metrics):
                # Receive task name length and name
                name_len_data = self.socket.recv(4)
                name_len = struct.unpack('I', name_len_data)[0]
                task_name = self.socket.recv(name_len).decode('utf-8')
                
                # Receive task metric value
                metric_value_data = self.socket.recv(8)
                metric_value = struct.unpack('d', metric_value_data)[0]
                
                task_metrics[task_name] = metric_value
            
            results[client_id] = {
                'id': id_val,
                'round_time': round_time,
                'throughput': throughput,
                'latency': latency,
                'packet_loss': packet_loss,
                'task_metrics': task_metrics
            }
        
        return results
    
    def close(self):
        """Close connection"""
        if self.socket:
            # Send EXIT command
            exit_command = struct.pack('III', 2, 0, 0)  # 2 = EXIT
            self.socket.send(exit_command)
            self.socket.close()
            logger.info("Connection closed")


def create_test_scenarios() -> List[Dict[str, any]]:
    """Create different test scenarios for comprehensive testing"""
    
    scenarios = []
    
    # Scenario 1: High priority, high resource allocation
    scenario1 = {
        "name": "High Priority High Resource",
        "description": "Tasks with high priority and high resource allocation",
        "allocations": {}
    }
    
    for client_id in range(3):
        scenario1["allocations"][client_id] = ResourceAllocation(
            select_ij={"task_0": 0.9, "task_1": 0.8, "task_2": 0.7},  # Most tasks selected
            q_ij={"task_0": 0.8, "task_1": 0.7, "task_2": 0.6},       # High compute
            B_ij={"task_0": 0.8, "task_1": 0.7, "task_2": 0.6},       # High bandwidth
            P_ij={"task_0": 0.7, "task_1": 0.6, "task_2": 0.5},       # High power
            Task_ij={"task_0": 0.9, "task_1": 0.8, "task_2": 0.7}     # High priority
        )
    scenarios.append(scenario1)
    
    # Scenario 2: Mixed priority and resource allocation
    scenario2 = {
        "name": "Mixed Priority Mixed Resource",
        "description": "Mixed priority tasks with varied resource allocation",
        "allocations": {}
    }
    
    for client_id in range(3):
        scenario2["allocations"][client_id] = ResourceAllocation(
            select_ij={"task_0": 0.8, "task_1": 0.4, "task_2": 0.9},  # Selective participation
            q_ij={"task_0": 0.6, "task_1": 0.2, "task_2": 0.8},       # Varied compute
            B_ij={"task_0": 0.5, "task_1": 0.3, "task_2": 0.7},       # Varied bandwidth
            P_ij={"task_0": 0.4, "task_1": 0.2, "task_2": 0.6},       # Varied power
            Task_ij={"task_0": 0.7, "task_1": 0.3, "task_2": 0.9}     # Mixed priority
        )
    scenarios.append(scenario2)
    
    # Scenario 3: Low resource, selective tasks
    scenario3 = {
        "name": "Low Resource Selective",
        "description": "Low resource allocation with selective task participation",
        "allocations": {}
    }
    
    for client_id in range(3):
        scenario3["allocations"][client_id] = ResourceAllocation(
            select_ij={"task_0": 0.6, "task_1": 0.3, "task_2": 0.5},  # Some tasks not selected
            q_ij={"task_0": 0.3, "task_1": 0.2, "task_2": 0.4},       # Low compute
            B_ij={"task_0": 0.3, "task_1": 0.2, "task_2": 0.4},       # Low bandwidth
            P_ij={"task_0": 0.2, "task_1": 0.1, "task_2": 0.3},       # Low power
            Task_ij={"task_0": 0.5, "task_1": 0.2, "task_2": 0.6}     # Low-medium priority
        )
    scenarios.append(scenario3)
    
    return scenarios


def analyze_results(scenario_name: str, allocations: Dict[int, ResourceAllocation], 
                   results: Dict[int, Dict], n_tasks: int):
    """Analyze and validate simulation results"""
    
    logger.info(f"\n=== ANALYSIS FOR {scenario_name} ===")
    
    # Analyze per-client results
    for client_id, result in results.items():
        allocation = allocations[client_id]
        
        logger.info(f"\nClient {client_id} Results:")
        logger.info(f"  Network Performance:")
        logger.info(f"    Throughput: {result['throughput']:.2f} Mbps")
        logger.info(f"    Latency: {result['latency']:.2f} ms")
        logger.info(f"    Packet Loss: {result['packet_loss']:.3f}")
        logger.info(f"    Round Time: {result['round_time']:.2f}s")
        
        logger.info(f"  Resource Allocation Summary:")
        total_compute = sum(allocation.q_ij.values())
        total_bandwidth = sum(allocation.B_ij.values())
        total_power = sum(allocation.P_ij.values())
        avg_priority = sum(allocation.Task_ij.values()) / len(allocation.Task_ij)
        selected_tasks = sum(1 for v in allocation.select_ij.values() if v >= 0.5)
        
        logger.info(f"    Total Compute: {total_compute:.2f}")
        logger.info(f"    Total Bandwidth: {total_bandwidth:.2f}")
        logger.info(f"    Total Power: {total_power:.2f}")
        logger.info(f"    Avg Priority: {avg_priority:.2f}")
        logger.info(f"    Selected Tasks: {selected_tasks}/{n_tasks}")
        
        logger.info(f"  Task-specific Performance:")
        for task_name, metric in result['task_metrics'].items():
            task_priority = allocation.Task_ij.get(task_name, 0.0)
            task_compute = allocation.q_ij.get(task_name, 0.0)
            task_selection = allocation.select_ij.get(task_name, 0.0)
            
            logger.info(f"    {task_name}: performance={metric:.3f}, "
                       f"priority={task_priority:.2f}, "
                       f"compute={task_compute:.2f}, "
                       f"selected={task_selection:.2f}")


def main():
    """Main test function"""
    logger.info("="*80)
    logger.info("COMPREHENSIVE NS3 MULTI-TASK FL SIMULATION TEST")
    logger.info("="*80)
    
    # Test parameters
    n_tasks = 3
    
    # Create test scenarios
    scenarios = create_test_scenarios()
    
    # Create tester
    tester = NS3MultiTaskTester()
    
    logger.info("Starting NS3 simulation...")
    logger.info("Make sure NS3 is running with: ./waf --run wifi_exp_multi_fl")
    
    if not tester.connect():
        logger.error("Failed to connect to NS3. Make sure the simulation is running.")
        return
    
    try:
        # Test each scenario
        for scenario in scenarios:
            logger.info(f"\n" + "="*60)
            logger.info(f"TESTING SCENARIO: {scenario['name']}")
            logger.info(f"Description: {scenario['description']}")
            logger.info("="*60)
            
            # Send allocations and run simulation
            tester.send_resource_allocations(scenario["allocations"], n_tasks)
            
            # Receive and analyze results
            results = tester.receive_results()
            analyze_results(scenario["name"], scenario["allocations"], results, n_tasks)
            
            # Small delay between scenarios
            time.sleep(1)
        
        logger.info(f"\n" + "="*80)
        logger.info("ALL SCENARIOS COMPLETED SUCCESSFULLY!")
        logger.info("="*80)
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        
    finally:
        tester.close()


if __name__ == "__main__":
    main()
