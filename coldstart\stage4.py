import numpy as np
from sklearn.metrics.pairwise import euclidean_distances
import pandas as pd


def summarize_virtual_coords(virtual_coords, method="mean"):
    """
    将单个任务的多个虚拟坐标压缩成一个 2D 表示向量。
    支持方法：mean, max
    """
    if method == "mean":
        return np.mean(virtual_coords, axis=0)
    elif method == "max":
        return np.max(virtual_coords, axis=0)
    else:
        raise ValueError("不支持的聚合方式")


def compute_task_similarity_from_virtual_coords(task_virtual_coord_list=None, gamma=0.0001):
    """
    输入为多个任务的虚拟坐标数组列表，每个元素 shape=(n_i, 2)
    输出任务间相似度矩阵 (n_tasks, n_tasks)
    1 表示完全相似
    > 0.5 表示相似
    <= 0.5 表示不相关
    """
    summarized_coords = [summarize_virtual_coords(vc) for vc in task_virtual_coord_list]
    summarized_coords = np.vstack(summarized_coords)  # shape=(n_tasks, 2)

    distance_matrix = euclidean_distances(summarized_coords)

    return distance_to_similarity(D=distance_matrix, gamma=gamma), distance_matrix


def distance_to_similarity(D, gamma):
    """
    将欧几里得距离矩阵转换为相似度矩阵（基于高斯核）

    参数:
        D (np.ndarray): 欧氏距离矩阵，形状 (n, n)
        gamma (float): 高斯核的参数，控制衰减速率

    返回:
        S (np.ndarray): 相似度矩阵，形状 (n, n)，值在 [0, 1] 区间内
    """
    # 计算平方距离矩阵
    D_squared = D ** 2

    # 应用高斯核
    S = np.exp(-gamma * D_squared)

    return S


# 示例运行
if __name__ == "__main__":
    # 模拟 3 个任务，每个任务有 500 个点在 2D 平面
    np.random.seed(42)
    task1_coords = np.random.rand(500, 2)
    task2_coords = np.random.rand(500, 2) + 1.0  # 模拟不同的分布
    task3_coords = np.random.rand(500, 2) + 2.0

    # 输入为虚拟坐标列表
    all_task_virtual_coords = [task1_coords, task2_coords, task3_coords]

    similarity_matrix = compute_task_similarity_from_virtual_coords(all_task_virtual_coords, gamma=0.01)

    similarity_df = pd.DataFrame(similarity_matrix,
                                 columns=[f"Task_{i}" for i in range(len(all_task_virtual_coords))],
                                 index=[f"Task_{i}" for i in range(len(all_task_virtual_coords))])

    print("任务间相似度矩阵：")
    print(similarity_df)
