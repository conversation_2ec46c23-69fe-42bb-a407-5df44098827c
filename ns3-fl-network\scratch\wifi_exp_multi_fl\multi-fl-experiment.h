/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#ifndef MULTI_FL_EXPERIMENT_H
#define MULTI_FL_EXPERIMENT_H

#include "ns3/command-line.h"
#include "ns3/config.h"
#include "ns3/uinteger.h"
#include "ns3/string.h"
#include "ns3/log.h"
#include "ns3/yans-wifi-helper.h"
#include "ns3/mobility-helper.h"
#include "ns3/ipv4-address-helper.h"
#include "ns3/on-off-helper.h"
#include "ns3/yans-wifi-channel.h"
#include "ns3/mobility-model.h"
#include "ns3/packet-socket-helper.h"
#include "ns3/packet-socket-address.h"
#include "multi-task-fl-sim-interface.h"
#include "multi-task-client-session.h"

#include <memory>
#include <string>
#include <cstdio>

namespace ns3 {
    /**
     * \ingroup multi-fl-experiment
     * \brief Sets up and runs multi-task FL experiments
     */
    class MultiTaskExperiment {
    public:
        /**
         * \brief Constructs Multi-Task FL Experiment
         * \param numClients      Number of clients in experiment
         * \param numTasks        Number of tasks
         * \param networkType     Network type (wifi or ethernet)
         * \param maxPacketSize   Max packet size for network
         * \param txGain          TX gain for wifi network
         * \param modelSize       Model size
         * \param dataRate        Datarate for server
         * \param bAsync          If running async experiment, true
         * \param pflSymProvider  pointer to multi-task FL sim interface
         * \param fp              File pointer for logging
         * \param round           Current round number
         */
        MultiTaskExperiment(int numClients, int numTasks, std::string &networkType, 
                          int maxPacketSize, double txGain, double modelSize,
                          std::string &dataRate, bool bAsync, 
                          MultiTaskFLSimProvider *pflSymProvider, FILE *fp, int round);

        /**
         * \brief Runs multi-task network experiment
         * \param clientSessions   map of <client id, client sessions>
         * \param timeOffset       Async, make timeline between rounds continuous
         * \return                 map of <client id, message>, messages to send back to multi-task FL sim
         */
        std::map<int, MultiTaskFLSimProvider::Message>
        RunMultiTaskNetwork(std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions, 
                           ns3::Time &timeOffset);

    private:
        /**
         * \brief Set position of node in network
         * \param node        Node to set position of
         * \param radius      Radius location of node
         * \param theta       Angular location of node
         */
        void SetPosition(Ptr <Node> node, double radius, double theta);

        /**
         * \brief Gets position of node
         * \param node   Node to get position of
         * \return       Vector of node position
         */
        Vector GetPosition(Ptr <Node> node);

        /**
         * \brief Sets up wifi network with multi-task considerations
         * \param c               Node container
         * \param clients         Client sessions with resource allocations
         * \return                Network device container
         */
        NetDeviceContainer SetupWifiNetwork(ns3::NodeContainer &c, 
                                           std::map<int, std::shared_ptr<MultiTaskClientSession> > &clients);

        /**
         * \brief Sets up ethernet network
         * \param c               Node container
         * \param clients         Client sessions
         * \return                Network device container
         */
        NetDeviceContainer SetupEthernetNetwork(NodeContainer &c, 
                                              std::map<int, std::shared_ptr<MultiTaskClientSession> > &clients);

        /**
         * \brief Configure transmission power based on resource allocation
         * \param device          Network device
         * \param powerAllocation Power allocation [0,1]
         */
        void ConfigureTransmissionPower(Ptr<NetDevice> device, double powerAllocation);

        /**
         * \brief Configure bandwidth based on resource allocation
         * \param device              Network device
         * \param bandwidthAllocation Bandwidth allocation [0,1]
         */
        void ConfigureBandwidth(Ptr<NetDevice> device, double bandwidthAllocation);

        /**
         * \brief Calculate task-specific metrics
         * \param clientSession   Client session
         * \param flowStats       Flow statistics
         * \return                Task-specific metrics map
         */
        std::map<std::string, double> CalculateTaskMetrics(
            std::shared_ptr<MultiTaskClientSession> clientSession,
            const Ptr<FlowMonitor::FlowStats> flowStats);

        /**
         * \brief Apply QoS based on task priorities
         * \param clientSessions  All client sessions
         */
        void ApplyTaskPriorityQoS(std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions);

        /**
         * \brief Log resource allocation details
         * \param clientSessions  All client sessions
         */
        void LogResourceAllocations(std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions);

        // Member variables
        int m_numClients;                           //!< Number of clients
        int m_numTasks;                            //!< Number of tasks
        std::string m_networkType;                 //!< Network type
        int m_maxPacketSize;                       //!< Maximum packet size
        double m_txGain;                           //!< Transmission gain
        double m_modelSize;                        //!< Model size
        std::string m_dataRate;                    //!< Data rate
        bool m_bAsync;                             //!< Async mode flag
        MultiTaskFLSimProvider *m_flSimProvider;  //!< FL simulation provider
        FILE *m_fp;                                //!< Log file pointer
        int m_round;                               //!< Current round number

        // Network configuration
        double m_baseTxPower;                      //!< Base transmission power (dBm)
        double m_maxBandwidth;                     //!< Maximum bandwidth (Mbps)
    };
}

#endif
