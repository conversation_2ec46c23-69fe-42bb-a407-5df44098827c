# from py_interface import *
from ctypes import *
import socket
import struct
import subprocess
import time

TCP_IP = '**************'
TCP_PORT = 8080
PATH = '../ns3-fl-network'
PROGRAM = 'wifi_exp'


class Network(object):
    def __init__(self, config):
        self.config = config
        self.num_clients = self.config.clients.total
        self.network_type = self.config.network.type

        # proc = subprocess.Popen('./waf build', shell=True, stdout=subprocess.PIPE, universal_newlines=True, cwd=PATH)
        # proc.wait()
        # if proc.returncode != 0:
        #     exit(-1)

        command = './waf --run "' + PROGRAM + ' --NumClients=' + str(self.num_clients) + ' --NetworkType=' + self.network_type
        command += ' --ModelSize=' + str(self.config.model.size)
        '''print(self.config.network)
        for net in self.config.network:
            if net == self.network_type:
                print(net.items())'''

        if self.network_type == 'wifi':
            command += ' --TxGain=' + str(self.config.network.wifi['tx_gain'])
            command += ' --MaxPacketSize=' + str(self.config.network.wifi['max_packet_size'])
        else:  # else assume ethernet
            command += ' --MaxPacketSize=' + str(self.config.network.ethernet['max_packet_size'])

        command += " --LearningModel=" + str(self.config.server)

        command += '"'
        print(command)
        while True:
            user_input = input("是否成功运行了ns3? 1表示成功, 其他输入表示失败：")
            print('')  # 换行
            if user_input in ['1']:
                break
            else:
                print("请启动ns3")
        # 训练代码时释放开，启动python代码时ns3，测试时用command手动启动ns3
        # proc = subprocess.Popen(command,
        #                         shell=True,
        #                         cwd=PATH,
        #                         stdout=subprocess.PIPE,
        #                         stderr=subprocess.STDOUT,  # 合并 stderr 到 stdout
        #                         universal_newlines=True  # 自动解码为字符串
        #                         )
        #
        # print("(python)正在启动 NS-3 模拟...")
        #
        # # 定义你希望等待的关键字
        # STARTUP_KEYWORD = "Waiting for connection"
        # # 记录是否已启动成功
        # started = False
        # # 逐行读取输出
        # try:
        #     for line in proc.stdout:
        #         line = line.strip()
        #         print(line)  # 打印输出（可选）
        #
        #         if STARTUP_KEYWORD in line:
        #             print("[INFO] (python)检测到模拟已启动！")
        #             started = True
        #             break  # 成功启动后退出循环
        #
        #     if not started:
        #         print("[ERROR] (python)模拟未能正常启动！")
        #         proc.terminate()
        #         proc.wait()
        #         exit(-1)
        #
        # except KeyboardInterrupt:
        #     print("\n[INFO] (python)用户中断，正在终止模拟...")
        #     proc.terminate()
        #     proc.wait()

    def parse_clients(self, clients):
        clients_to_send = [0 for _ in range(self.num_clients)]
        for client in clients:
            clients_to_send[client] = 1
        return clients_to_send

    def connect(self):
        print("connecting to ns3 tcp server...")
        while True:
            try:
                self.s = socket.create_connection((TCP_IP, TCP_PORT,))
                break
            except ConnectionRefusedError:
                print("Connection refused, retrying in 5 seconds...")
                time.sleep(5)
        print("ns3 tcp connected")

    def sendRequest(self, *, requestType: int, array: list):
        # TODO (python)sending
        print("(python)sending")
        print(array)  # array is a list of client ids
        message = struct.pack("II", requestType, len(array))
        self.s.send(message)
        # for the total number of clients
        # is the index in lit at client.id equal
        for ele in array:
            self.s.send(struct.pack("I", ele))

        resp = self.s.recv(8)
        # TODO (python)response
        print("(python)response")
        print(resp)
        if len(resp) < 8:
            print('python has no receive 8byte totally', len(resp), resp)
        command, nItems = struct.unpack("II", resp) 
        ret = {}
        for i in range(nItems):
            dr = self.s.recv(8 * 3)  # 每次接收 24 字节（8 × 3），对应一条记录
            eid, roundTime, throughput = struct.unpack("Qdd", dr)
            temp = {"roundTime": roundTime, "throughput": throughput}
            ret[eid] = temp
        return ret

    def sendAsyncRequest(self, *, requestType: int, array: list):
        print("(python)sending")
        print(array)
        message = struct.pack("II", requestType, len(array))
        self.s.send(message)
        # for the total number of clients
        # is the index in lit at client.id equal
        for ele in array:
            self.s.send(struct.pack("I", ele))

    def readAsyncResponse(self):
        resp = self.s.recv(8)
        print("(python)resp")
        print(resp)
        if len(resp) < 8:
            print(len(resp), resp)
        command, nItems = struct.unpack("II", resp)

        print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        print(f'command={command}, nItems={nItems}')
        if command == 3:
            return 'end'
        ret = {}
        for i in range(nItems):
            dr = self.s.recv(8 * 4)
            eid, startTime, endTime, throughput = struct.unpack("Qddd", dr)
            temp = {"startTime": startTime, "endTime": endTime, "throughput": throughput}
            ret[eid] = temp
        return ret

    def disconnect(self):
        # self.sendAsyncRequest(requestType=2, array=[])
        self.s.close()
