#!/usr/bin/env python3
"""
Quick test for NS3 multi-task federated learning simulation
Validates that all ResourceAllocation actions are properly configured
"""

import socket
import struct
import logging
from typing import Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ResourceAllocation:
    def __init__(self, select_ij, q_ij, B_ij, P_ij, Task_ij):
        self.select_ij = select_ij
        self.q_ij = q_ij
        self.B_ij = B_ij
        self.P_ij = P_ij
        self.Task_ij = Task_ij


def create_test_allocation() -> Dict[int, ResourceAllocation]:
    """Create a simple test allocation to verify all actions work"""
    
    allocations = {}
    
    # Client 0: High priority task_0, medium task_1, low task_2
    allocations[0] = ResourceAllocation(
        select_ij={"task_0": 0.9, "task_1": 0.7, "task_2": 0.3},  # task_2 won't be selected
        q_ij={"task_0": 0.8, "task_1": 0.5, "task_2": 0.2},       # High compute for task_0
        B_ij={"task_0": 0.7, "task_1": 0.4, "task_2": 0.2},       # High bandwidth for task_0
        P_ij={"task_0": 0.6, "task_1": 0.3, "task_2": 0.1},       # High power for task_0
        Task_ij={"task_0": 0.9, "task_1": 0.6, "task_2": 0.2}     # task_0 highest priority
    )
    
    # Client 1: Balanced allocation
    allocations[1] = ResourceAllocation(
        select_ij={"task_0": 0.6, "task_1": 0.8, "task_2": 0.7},  # All tasks selected
        q_ij={"task_0": 0.5, "task_1": 0.6, "task_2": 0.4},       # Balanced compute
        B_ij={"task_0": 0.4, "task_1": 0.5, "task_2": 0.3},       # Balanced bandwidth
        P_ij={"task_0": 0.3, "task_1": 0.4, "task_2": 0.2},       # Balanced power
        Task_ij={"task_0": 0.5, "task_1": 0.8, "task_2": 0.4}     # task_1 highest priority
    )
    
    # Client 2: Low resource allocation
    allocations[2] = ResourceAllocation(
        select_ij={"task_0": 0.4, "task_1": 0.6, "task_2": 0.8},  # Only task_1, task_2 selected
        q_ij={"task_0": 0.2, "task_1": 0.3, "task_2": 0.4},       # Low compute overall
        B_ij={"task_0": 0.2, "task_1": 0.3, "task_2": 0.4},       # Low bandwidth overall
        P_ij={"task_0": 0.1, "task_1": 0.2, "task_2": 0.3},       # Low power overall
        Task_ij={"task_0": 0.3, "task_1": 0.5, "task_2": 0.7}     # task_2 highest priority
    )
    
    return allocations


def send_test_to_ns3():
    """Send test allocation to NS3 and receive results"""
    
    # Test parameters
    n_clients = 3
    n_tasks = 3
    host = 'localhost'
    port = 8080
    
    # Create test allocation
    allocations = create_test_allocation()
    
    logger.info("="*60)
    logger.info("QUICK NS3 MULTI-TASK TEST")
    logger.info("="*60)
    
    # Log test allocation
    logger.info("Test Resource Allocation:")
    for client_id, alloc in allocations.items():
        logger.info(f"Client {client_id}:")
        for task_id in range(n_tasks):
            task_name = f"task_{task_id}"
            logger.info(f"  {task_name}: select={alloc.select_ij[task_name]:.1f}, "
                       f"compute={alloc.q_ij[task_name]:.1f}, "
                       f"bandwidth={alloc.B_ij[task_name]:.1f}, "
                       f"power={alloc.P_ij[task_name]:.1f}, "
                       f"priority={alloc.Task_ij[task_name]:.1f}")
    
    # Connect to NS3
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((host, port))
        logger.info(f"Connected to NS3 at {host}:{port}")
        
        # Send command
        command_data = struct.pack('III', 1, n_clients, n_tasks)  # RUN_SIMULATION
        sock.send(command_data)
        
        # Send allocations
        for client_id in sorted(allocations.keys()):
            alloc = allocations[client_id]
            
            # Send client ID
            sock.send(struct.pack('I', client_id))
            
            # Send allocation data (5 values per task)
            for task_id in range(n_tasks):
                task_name = f"task_{task_id}"
                values = [
                    alloc.select_ij[task_name],
                    alloc.q_ij[task_name],
                    alloc.B_ij[task_name],
                    alloc.P_ij[task_name],
                    alloc.Task_ij[task_name]
                ]
                for value in values:
                    sock.send(struct.pack('d', value))
        
        logger.info("Sent resource allocations to NS3")
        
        # Receive results
        n_results_data = sock.recv(4)
        n_results = struct.unpack('I', n_results_data)[0]
        
        logger.info(f"Receiving results for {n_results} clients...")
        
        for _ in range(n_results):
            # Receive client ID
            client_id_data = sock.recv(4)
            client_id = struct.unpack('I', client_id_data)[0]
            
            # Receive basic metrics
            metrics_data = sock.recv(8 + 8 + 8 + 8 + 8)  # 5 doubles
            id_val, round_time, throughput, latency, packet_loss = struct.unpack('Qdddd', metrics_data)
            
            # Receive task metrics
            n_task_metrics_data = sock.recv(4)
            n_task_metrics = struct.unpack('I', n_task_metrics_data)[0]
            
            task_metrics = {}
            for _ in range(n_task_metrics):
                # Task name
                name_len_data = sock.recv(4)
                name_len = struct.unpack('I', name_len_data)[0]
                task_name = sock.recv(name_len).decode('utf-8')
                
                # Task metric
                metric_data = sock.recv(8)
                metric_value = struct.unpack('d', metric_data)[0]
                task_metrics[task_name] = metric_value
            
            # Log results
            logger.info(f"Client {client_id} Results:")
            logger.info(f"  Throughput: {throughput:.2f} Mbps")
            logger.info(f"  Latency: {latency:.2f} ms")
            logger.info(f"  Packet Loss: {packet_loss:.3f}")
            logger.info(f"  Round Time: {round_time:.2f}s")
            logger.info(f"  Task Metrics: {task_metrics}")
        
        # Send exit command
        exit_command = struct.pack('III', 2, 0, 0)  # EXIT
        sock.send(exit_command)
        sock.close()
        
        logger.info("="*60)
        logger.info("TEST COMPLETED SUCCESSFULLY!")
        logger.info("="*60)
        
        # Validation summary
        logger.info("VALIDATION SUMMARY:")
        logger.info("✅ Socket communication working")
        logger.info("✅ Resource allocations sent successfully")
        logger.info("✅ Simulation results received")
        logger.info("✅ All 5 action types (select_ij, q_ij, B_ij, P_ij, Task_ij) processed")
        
    except ConnectionRefusedError:
        logger.error("❌ Could not connect to NS3 simulation")
        logger.error("Make sure NS3 is running with: ./waf --run wifi_exp_multi_fl")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")


def main():
    """Main function"""
    logger.info("Starting quick NS3 multi-task test...")
    logger.info("Make sure NS3 simulation is running!")
    logger.info("Command: ./waf --run wifi_exp_multi_fl")
    logger.info("")
    
    send_test_to_ns3()


if __name__ == "__main__":
    main()
