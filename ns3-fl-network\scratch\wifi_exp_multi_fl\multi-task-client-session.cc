/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-task-client-session.h"
#include "ns3/socket.h"
#include "ns3/log.h"

namespace ns3 {

    NS_LOG_COMPONENT_DEFINE("MultiTaskClientSession");

    MultiTaskClientSession::MultiTaskClientSession(int clientID_, double radius_, double theta_, int numTasks_)
        : m_clientID(clientID_), m_radius(radius_), m_theta(theta_), m_numTasks(numTasks_), m_inRound(false) {
        
        // Initialize resource allocation with default values
        for (int i = 0; i < m_numTasks; i++) {
            std::string taskName = "task_" + std::to_string(i);
            m_resourceAllocation.select_ij[taskName] = 0.0;
            m_resourceAllocation.q_ij[taskName] = 0.0;
            m_resourceAllocation.B_ij[taskName] = 0.0;
            m_resourceAllocation.P_ij[taskName] = 0.0;
            m_resourceAllocation.Task_ij[taskName] = 0.0;
        }
        
        NS_LOG_INFO("Created MultiTaskClientSession for client " << clientID_ << " with " << numTasks_ << " tasks");
    }

    Ptr<ns3::Socket> MultiTaskClientSession::GetClient() {
        return m_client;
    }

    void MultiTaskClientSession::SetClient(Ptr<ns3::Socket> client) {
        m_client = client;
    }

    bool MultiTaskClientSession::GetInRound() {
        return m_inRound;
    }

    void MultiTaskClientSession::SetInRound(bool inRound) {
        m_inRound = inRound;
    }

    double MultiTaskClientSession::GetRadius() {
        return m_radius;
    }

    double MultiTaskClientSession::GetTheta() {
        return m_theta;
    }

    int MultiTaskClientSession::GetClientId() {
        return m_clientID;
    }

    int MultiTaskClientSession::GetNumTasks() {
        return m_numTasks;
    }

    void MultiTaskClientSession::SetResourceAllocation(const ResourceAllocation& allocation) {
        m_resourceAllocation = allocation;
        NS_LOG_DEBUG("Set resource allocation for client " << m_clientID);
    }

    const ResourceAllocation& MultiTaskClientSession::GetResourceAllocation() const {
        return m_resourceAllocation;
    }

    double MultiTaskClientSession::GetTaskSelectionProbability(const std::string& taskName) const {
        auto it = m_resourceAllocation.select_ij.find(taskName);
        return (it != m_resourceAllocation.select_ij.end()) ? it->second : 0.0;
    }

    double MultiTaskClientSession::GetTaskComputeAllocation(const std::string& taskName) const {
        auto it = m_resourceAllocation.q_ij.find(taskName);
        return (it != m_resourceAllocation.q_ij.end()) ? it->second : 0.0;
    }

    double MultiTaskClientSession::GetTaskBandwidthAllocation(const std::string& taskName) const {
        auto it = m_resourceAllocation.B_ij.find(taskName);
        return (it != m_resourceAllocation.B_ij.end()) ? it->second : 0.0;
    }

    double MultiTaskClientSession::GetTaskPowerAllocation(const std::string& taskName) const {
        auto it = m_resourceAllocation.P_ij.find(taskName);
        return (it != m_resourceAllocation.P_ij.end()) ? it->second : 0.0;
    }

    double MultiTaskClientSession::GetTaskPriority(const std::string& taskName) const {
        auto it = m_resourceAllocation.Task_ij.find(taskName);
        return (it != m_resourceAllocation.Task_ij.end()) ? it->second : 0.0;
    }

    double MultiTaskClientSession::GetTotalPowerAllocation() const {
        double total = 0.0;
        for (const auto& pair : m_resourceAllocation.P_ij) {
            total += pair.second;
        }
        return total;
    }

    double MultiTaskClientSession::GetAverageSelectionProbability() const {
        if (m_resourceAllocation.select_ij.empty()) {
            return 0.0;
        }
        
        double total = 0.0;
        for (const auto& pair : m_resourceAllocation.select_ij) {
            total += pair.second;
        }
        return total / m_resourceAllocation.select_ij.size();
    }

    // MultiTaskClientSessionManager implementation
    MultiTaskClientSessionManager::MultiTaskClientSessionManager(
        std::map<int, std::shared_ptr<MultiTaskClientSession> > &clientSessions)
        : m_clientSessionById(clientSessions), m_nInRound(0) {
    }

    int MultiTaskClientSessionManager::ResolveToId(ns3::Ipv4Address &address) {
        auto it = m_clientSessionByAddress.find(address);
        if (it != m_clientSessionByAddress.end()) {
            return it->second;
        }
        return -1; // Not found
    }

    int MultiTaskClientSessionManager::ResolveToIdFromServer(ns3::Ptr<ns3::Socket> socket) {
        // Implementation would depend on how sockets are mapped to client IDs
        // This is a simplified version
        for (const auto& pair : m_clientSessionById) {
            if (pair.second->GetClient() == socket) {
                return pair.first;
            }
        }
        return -1; // Not found
    }

    void MultiTaskClientSessionManager::Close() {
        for (auto& pair : m_clientSessionById) {
            if (pair.second->GetClient()) {
                pair.second->GetClient()->Close();
            }
        }
    }

    ns3::Ipv4Address MultiTaskClientSessionManager::ResolveToAddress(int id) {
        for (const auto& pair : m_clientSessionByAddress) {
            if (pair.second == id) {
                return pair.first;
            }
        }
        return ns3::Ipv4Address(); // Return invalid address if not found
    }

    std::vector<int> MultiTaskClientSessionManager::GetSelectedClientsForTask(
        const std::string& taskName, double threshold) {
        
        std::vector<int> selectedClients;
        
        for (const auto& pair : m_clientSessionById) {
            int clientId = pair.first;
            auto client = pair.second;
            
            double selectionProb = client->GetTaskSelectionProbability(taskName);
            if (selectionProb >= threshold) {
                selectedClients.push_back(clientId);
            }
        }
        
        return selectedClients;
    }
}
