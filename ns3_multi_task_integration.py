#!/usr/bin/env python3
"""
Integration module for NS3 multi-task federated learning simulation
"""

import socket
import struct
import time
import logging
import subprocess
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass

from multi_task_client import ResourceAllocation, NetworkMetrics

logger = logging.getLogger(__name__)


class NS3MultiTaskSimulator:
    """NS3 multi-task federated learning network simulator"""
    
    def __init__(self, host='localhost', port=8080, ns3_path='ns3-fl-network'):
        self.host = host
        self.port = port
        self.ns3_path = ns3_path
        self.socket = None
        self.ns3_process = None
        self.connected = False
        
    def start_ns3_simulation(self, num_clients: int, num_tasks: int, **kwargs):
        """Start NS3 simulation process"""
        try:
            # Build command
            cmd = [
                './waf', '--run', 
                f'wifi_exp_multi_fl --NumClients={num_clients} --NumTasks={num_tasks}'
            ]
            
            # Add optional parameters
            if 'tx_gain' in kwargs:
                cmd[-1] += f' --TxGain={kwargs["tx_gain"]}'
            if 'network_type' in kwargs:
                cmd[-1] += f' --NetworkType={kwargs["network_type"]}'
            if 'max_packet_size' in kwargs:
                cmd[-1] += f' --MaxPacketSize={kwargs["max_packet_size"]}'
            if 'data_rate' in kwargs:
                cmd[-1] += f' --DataRate={kwargs["data_rate"]}'
            
            logger.info(f"Starting NS3 simulation: {' '.join(cmd)}")
            
            # Start process
            self.ns3_process = subprocess.Popen(
                cmd, 
                cwd=self.ns3_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give NS3 time to start and listen
            time.sleep(2)
            
            logger.info("NS3 simulation process started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start NS3 simulation: {e}")
            return False
    
    def connect(self, timeout=10):
        """Connect to NS3 simulation"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.settimeout(5)
                self.socket.connect((self.host, self.port))
                self.connected = True
                logger.info(f"Connected to NS3 simulation at {self.host}:{self.port}")
                return True
                
            except (socket.error, ConnectionRefusedError):
                if self.socket:
                    self.socket.close()
                time.sleep(1)
                continue
                
        logger.error(f"Failed to connect to NS3 simulation within {timeout} seconds")
        return False
    
    def send_resource_allocations(self, allocations: Dict[int, ResourceAllocation], num_tasks: int):
        """Send resource allocations to NS3"""
        if not self.connected:
            raise RuntimeError("Not connected to NS3 simulation")
        
        num_clients = len(allocations)
        
        # Send command: RUN_SIMULATION (1), num_clients, num_tasks
        command_data = struct.pack('III', 1, num_clients, num_tasks)
        self.socket.send(command_data)
        
        # Send resource allocations for each client
        for client_id in sorted(allocations.keys()):
            allocation = allocations[client_id]
            
            # Send client ID
            self.socket.send(struct.pack('I', client_id))
            
            # Send allocation data (5 values per task)
            allocation_values = []
            for task_id in range(num_tasks):
                task_name = f"task_{task_id}"
                allocation_values.extend([
                    allocation.select_ij.get(task_name, 0.0),
                    allocation.q_ij.get(task_name, 0.0),
                    allocation.B_ij.get(task_name, 0.0),
                    allocation.P_ij.get(task_name, 0.0),
                    allocation.Task_ij.get(task_name, 0.0)
                ])
            
            # Send all values as doubles
            for value in allocation_values:
                self.socket.send(struct.pack('d', value))
        
        logger.debug(f"Sent resource allocations for {num_clients} clients")
    
    def receive_network_metrics(self) -> Dict[int, NetworkMetrics]:
        """Receive network metrics from NS3"""
        if not self.connected:
            raise RuntimeError("Not connected to NS3 simulation")
        
        # Receive number of results
        n_results_data = self.socket.recv(4)
        if len(n_results_data) != 4:
            raise RuntimeError("Failed to receive number of results")
        
        n_results = struct.unpack('I', n_results_data)[0]
        
        network_metrics = {}
        for _ in range(n_results):
            # Receive client ID
            client_id_data = self.socket.recv(4)
            client_id = struct.unpack('I', client_id_data)[0]
            
            # Receive basic metrics (id, roundTime, throughput, latency, packetLoss)
            metrics_data = self.socket.recv(8 + 8 + 8 + 8 + 8)  # 5 doubles
            id_val, round_time, throughput, latency, packet_loss = struct.unpack('Qdddd', metrics_data)
            
            # Receive task-specific metrics (we'll store them but not use in NetworkMetrics)
            n_task_metrics_data = self.socket.recv(4)
            n_task_metrics = struct.unpack('I', n_task_metrics_data)[0]
            
            task_metrics = {}
            for _ in range(n_task_metrics):
                # Receive task name length
                name_len_data = self.socket.recv(4)
                name_len = struct.unpack('I', name_len_data)[0]
                
                # Receive task name
                task_name = self.socket.recv(name_len).decode('utf-8')
                
                # Receive task metric value
                metric_value_data = self.socket.recv(8)
                metric_value = struct.unpack('d', metric_value_data)[0]
                
                task_metrics[task_name] = metric_value
            
            # Create NetworkMetrics object
            network_metrics[client_id] = NetworkMetrics(
                client_id=client_id,
                round_time=round_time,
                throughput=throughput,
                latency=latency,
                packet_loss=packet_loss
            )
        
        logger.info(f"Received network metrics for {n_results} clients")
        return network_metrics
    
    def simulate_network(self, allocations: Dict[int, ResourceAllocation], num_tasks: int) -> Dict[int, NetworkMetrics]:
        """Run complete network simulation"""
        try:
            # Send resource allocations
            self.send_resource_allocations(allocations, num_tasks)
            
            # Receive results
            network_metrics = self.receive_network_metrics()
            
            return network_metrics
            
        except Exception as e:
            logger.error(f"Network simulation failed: {e}")
            raise
    
    def close(self):
        """Close connection and stop NS3 process"""
        if self.connected and self.socket:
            try:
                # Send EXIT command
                exit_command = struct.pack('III', 2, 0, 0)  # 2 = EXIT
                self.socket.send(exit_command)
            except:
                pass
            
            self.socket.close()
            self.connected = False
            logger.info("NS3 connection closed")
        
        if self.ns3_process:
            try:
                self.ns3_process.terminate()
                self.ns3_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ns3_process.kill()
            logger.info("NS3 process stopped")


def integrate_with_multi_task_server():
    """Integration function for multi_task_server.py"""
    
    def _simulate_model_transmission_with_ns3_multi_task(
        self, clients, resource_allocations, num_tasks=3, **kwargs):
        """
        Enhanced NS3 simulation method for multi-task FL
        
        This method should replace _simulate_model_transmission_with_ns3 
        in multi_task_server.py
        """
        
        if not hasattr(self, '_ns3_simulator'):
            # Initialize NS3 simulator
            self._ns3_simulator = NS3MultiTaskSimulator()
            
            # Start NS3 simulation
            if not self._ns3_simulator.start_ns3_simulation(
                num_clients=len(clients), 
                num_tasks=num_tasks,
                **kwargs
            ):
                raise RuntimeError("Failed to start NS3 simulation")
            
            # Connect to simulation
            if not self._ns3_simulator.connect():
                raise RuntimeError("Failed to connect to NS3 simulation")
        
        try:
            # Run network simulation
            network_metrics = self._ns3_simulator.simulate_network(
                resource_allocations, num_tasks
            )
            
            logger.info(f"NS3 multi-task simulation completed for {len(network_metrics)} clients")
            return network_metrics
            
        except Exception as e:
            logger.error(f"NS3 multi-task simulation failed: {e}")
            # Fallback to original simulation
            return self._simulate_model_transmission_with_ns3_fallback(clients, resource_allocations)
    
    def _simulate_model_transmission_with_ns3_fallback(self, clients, resource_allocations):
        """Fallback simulation when NS3 fails"""
        logger.warning("Using fallback network simulation")
        
        network_metrics = {}
        for client in clients:
            if client.client_id in resource_allocations:
                allocation = resource_allocations[client.client_id]
                
                # Simple power-based simulation
                total_power = sum(allocation.P_ij.values())
                power_factor = max(0.1, total_power)
                
                network_metrics[client.client_id] = NetworkMetrics(
                    client_id=client.client_id,
                    round_time=max(0.5, 3.0 / power_factor),
                    throughput=max(100, 500 * power_factor),
                    latency=max(10, 100 / power_factor),
                    packet_loss=max(0.0, 0.1 - power_factor * 0.1)
                )
        
        return network_metrics
    
    return _simulate_model_transmission_with_ns3_multi_task


# Example usage and testing
if __name__ == "__main__":
    # Test the NS3 integration
    logging.basicConfig(level=logging.INFO)
    
    # Create sample allocations
    allocations = {}
    for client_id in range(3):
        allocations[client_id] = ResourceAllocation(
            select_ij={"task_0": 0.8, "task_1": 0.6, "task_2": 0.9},
            q_ij={"task_0": 0.5, "task_1": 0.3, "task_2": 0.7},
            B_ij={"task_0": 0.4, "task_1": 0.6, "task_2": 0.5},
            P_ij={"task_0": 0.3, "task_1": 0.4, "task_2": 0.2},
            Task_ij={"task_0": 0.9, "task_1": 0.7, "task_2": 0.8}
        )
    
    # Test simulation
    simulator = NS3MultiTaskSimulator()
    
    try:
        # Start and connect
        if simulator.start_ns3_simulation(num_clients=3, num_tasks=3):
            if simulator.connect():
                # Run simulation
                results = simulator.simulate_network(allocations, num_tasks=3)
                
                # Print results
                for client_id, metrics in results.items():
                    print(f"Client {client_id}: "
                          f"throughput={metrics.throughput:.2f} Mbps, "
                          f"latency={metrics.latency:.2f} ms")
    
    finally:
        simulator.close()
