import numpy as np
from sklearn.metrics.pairwise import euclidean_distances
from pyswarm import pso


def p_stable_distribution(p, num_vectors, d):
    """生成 p-stable 分布的投影向量，支持 p=1(<PERSON><PERSON>chy) 或 p=2(Gaussian)"""
    if p == 1:
        return np.random.standard_cauchy(size=(d, num_vectors))
    elif p == 2:
        return np.random.normal(size=(d, num_vectors))
    else:
        raise ValueError("仅支持 p=1 (Cauchy) 或 p=2 (Gaussian)")


def project_centroids(centroids, P):
    """
    使用公式 (3) 将子区域质心从 d 维空间投影到 2 维虚拟平面
    centroids: (n, d)
    P: 投影矩阵 (d, 2)
    return: 投影后的虚拟坐标 (n, 2)
    """
    return centroids @ P


def compute_distances(X):
    """计算任意点集合 X 的两两欧氏距离矩阵"""
    return euclidean_distances(X, X)


def objective_projection(P_flat, centroids, original_distances, delta_p):
    """
    粒子群优化目标函数
    P_flat: 展平后的投影矩阵参数 (2*d,)
    centroids: 原始质心 (n, d)
    original_distances: 原始质心之间的距离 (n, n)
    delta_p: 约束阈值
    """
    d = centroids.shape[1]
    P = P_flat.reshape(d, 2)  # 恢复为 (d, 2) 矩阵
    projected = project_centroids(centroids, P)
    projected_distances = compute_distances(projected)

    # 公式(4)
    # 投影向量间的相关性
    corr_p = np.corrcoef(P[:, 0], P[:, 1])[0, 1]
    # 距离矩阵间的相关性
    corr_dist = np.corrcoef(original_distances.flatten(), projected_distances.flatten())[0, 1]

    # 如果违反了相关性小于 delta_p 的约束，加惩罚
    penalty = 0.0
    if abs(corr_p) > delta_p:
        penalty = 1000 * abs(corr_p - delta_p)

    return abs(corr_p - corr_dist) + penalty


# 主执行函数
def run_virtual_coordinate_optimization(centroids, p=2, delta_p=0.05, swarmsize=20, maxiter=50):
    n, d = centroids.shape
    original_distances = compute_distances(centroids)

    # 初始化参数范围 [-1, 1]，有 2*d 个变量（投影矩阵为 d×2）
    lb = [-1.0] * (2 * d)
    ub = [1.0] * (2 * d)

    # 粒子群优化求解投影矩阵
    best_params, _ = pso(
        objective_projection, lb, ub,
        args=(centroids, original_distances, delta_p),
        swarmsize=swarmsize, maxiter=maxiter
    )

    # 得到最优投影矩阵并计算虚拟坐标
    P_opt = best_params.reshape(d, 2)
    virtual_coords = project_centroids(centroids, P_opt)

    return virtual_coords, P_opt


# 示例运行
if __name__ == "__main__":
    # 生成随机子区域质心数据
    n_centroids = 100
    d_dimension = 128
    centroids = np.random.rand(n_centroids, d_dimension)  # centroids 是这些特征在高维空间中的表示

    # 运行整体流程
    virtual_coords, P_opt = run_virtual_coordinate_optimization(centroids)
    print(virtual_coords.shape)
    print("前5个虚拟坐标（投影到2D平面）：")
    print(virtual_coords[:5])  # 坐标系下的点坐标
    print("投影矩阵 P (d x 2)：")
    print(P_opt)  # 地图坐标系、坐标转换器
