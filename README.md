# Multi-Task Federated Learning with Task Correlation-Driven AI & Network Co-optimization

This project implements a comprehensive multi-task federated learning system that combines task similarity analysis, MAPPO (Multi-Agent Proximal Policy Optimization) resource allocation, and NS3 network simulation to achieve task correlation-driven AI and network co-optimization in edge learning environments.

## 🎯 Key Features

### 1. **Multi-Task Federated Learning**
- Simultaneous handling of multiple tasks (MNIST, CIFAR10, Air Quality) on each client
- Task similarity analysis using existing Euclidean distance matrix from coldstart methods
- Intelligent model sharing vs independent training decisions based on task correlations

### 2. **MAPPO Resource Allocation**
- Multi-agent reinforcement learning for computing resource priority allocation
- Wireless network power distribution optimization
- Coordinated resource management across multiple clients

### 3. **NS3 Network Simulation Integration**
- Real-time network simulation using existing NS3 interface (ns3_socket/network.py)
- Network metrics (latency, throughput, packet loss) fed back to MAPPO state space
- Realistic wireless network environment modeling

### 4. **AI-Network Co-optimization**
- Task similarity drives model sharing decisions to save resources
- MAPPO optimizes both computing and network resources jointly
- Network feedback improves future resource allocation decisions

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Multi-Task FL Server                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   FL Coordinator │  │ Model Aggregator │  │ MAPPO Coordinator│ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │           │           │
        ┌───────────▼──┐ ┌──────▼──────┐ ┌──▼──────────┐
        │   Client 1   │ │   Client 2  │ │   Client N  │
        │              │ │             │ │             │
        │ ┌──────────┐ │ │ ┌─────────┐ │ │ ┌─────────┐ │
        │ │Task Sim. │ │ │ │Task Sim.│ │ │ │Task Sim.│ │
        │ │Analyzer  │ │ │ │Analyzer │ │ │ │Analyzer │ │
        │ └──────────┘ │ │ └─────────┘ │ │ └─────────┘ │
        │ ┌──────────┐ │ │ ┌─────────┐ │ │ ┌─────────┐ │
        │ │Multi-Task│ │ │ │Multi-   │ │ │ │Multi-   │ │
        │ │Manager   │ │ │ │Task Mgr │ │ │ │Task Mgr │ │
        │ └──────────┘ │ │ └─────────┘ │ │ └─────────┘ │
        └──────────────┘ └─────────────┘ └─────────────┘
                    │           │           │
                    └───────────┼───────────┘
                                │
                    ┌───────────▼───────────┐
                    │   NS3 Network Sim     │
                    │  ┌─────────────────┐  │
                    │  │ Wireless Network│  │
                    │  │ Power & Bandwidth│  │
                    │  │ Allocation      │  │
                    │  └─────────────────┘  │
                    └───────────────────────┘
```

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Install dependencies
pip install torch torchvision numpy pandas scikit-learn

# Ensure NS3 is properly configured (see ns3_socket/network.py)
```

### 2. Run System Demo
```bash
# Run complete system demonstration
python demo_system.py
```

### 3. Run Full Experiment
```bash
# Single experiment
python run_multi_task_fl.py --mode single --num-clients 5 --num-rounds 50

# Ablation study
python run_multi_task_fl.py --mode ablation
```

### 4. Custom Configuration
```bash
# Custom experiment with specific parameters
python run_multi_task_fl.py \
    --num-clients 8 \
    --num-rounds 100 \
    --similarity-threshold 0.6 \
    --tasks mnist cifar10 \
    --network-type wifi \
    --mappo-lr 0.0003
```

## 📁 Project Structure

```
multi-task-similarity/
├── multi_task_client.py          # Multi-task FL client implementation
├── mappo_allocator.py            # MAPPO resource allocation
├── multi_task_server.py          # Multi-task FL server
├── run_multi_task_fl.py          # Main experiment runner
├── demo_system.py               # System demonstration
├── README.md                    # This file
│
├── coldstart/                   # Existing task similarity analysis
│   ├── coldstart_stage.py      # Euclidean distance matrix computation
│   └── data_utils.py           # Data loading utilities
│
├── federated_learning/          # Existing FL framework
│   ├── server.py               # Base server implementation
│   └── client.py               # Base client implementation
│
├── ns3_socket/                  # Existing NS3 interface
│   └── network.py              # NS3 network simulation interface
│
└── data/                        # Dataset storage
    ├── MNIST/
    ├── cifar-10-batches-py/
    └── air_quality/
```

## 🔧 Core Components

### 1. Multi-Task Client (`multi_task_client.py`)
- **Task Management**: Handles multiple tasks (MNIST, CIFAR10, Air Quality) simultaneously
- **Similarity Analysis**: Uses existing Euclidean distance matrix method from coldstart
- **Resource Allocation**: Applies MAPPO resource decisions to training process
- **Network Integration**: Incorporates NS3 network metrics into training decisions

### 2. MAPPO Resource Allocator (`mappo_allocator.py`)
- **Multi-Agent RL**: MAPPO algorithm for coordinated resource allocation
- **Resource Types**: CPU, GPU, memory allocation per task + wireless transmission power
- **State Space**: Task performance + resource utilization + network metrics + task similarity
- **Action Space**: Resource allocation decisions for all tasks and network power

### 3. Multi-Task Server (`multi_task_server.py`)
- **FL Coordination**: Manages federated learning rounds with multiple tasks
- **MAPPO Integration**: Coordinates resource allocation across clients
- **NS3 Integration**: Manages network simulation and metric collection
- **Model Aggregation**: Handles aggregation of multi-task models

## 🔄 Complete Workflow

1. **Initialization**
   - Server creates multi-task clients
   - Each client analyzes task similarity using existing Euclidean distance matrix
   - MAPPO agents are initialized for resource allocation

2. **Federated Learning Round**
   - **State Collection**: Clients report current state (task performance, resource usage, network metrics)
   - **MAPPO Allocation**: Multi-agent RL determines optimal resource allocation
   - **NS3 Simulation**: Network simulation based on power allocation decisions
   - **Client Training**: Clients train with allocated resources and network constraints
   - **Model Aggregation**: Server aggregates models based on task similarity groups
   - **Reward Calculation**: MAPPO agents receive rewards based on performance and efficiency

3. **AI-Network Co-optimization**
   - Task similarity drives model sharing decisions (save resources)
   - MAPPO optimizes compute resources and wireless power jointly
   - Network feedback improves future allocation decisions
   - Continuous adaptation to changing conditions

## 📊 Key Innovations

### 1. **Task Correlation-Driven Model Sharing**
- Uses existing Euclidean distance matrix method for task similarity
- Automatic decision between shared vs independent training
- Resource savings through intelligent model sharing

### 2. **MAPPO Resource Allocation**
- Multi-agent approach for coordinated resource management
- Simultaneous optimization of computing and network resources
- Adaptive learning based on performance feedback

### 3. **NS3 Network Integration**
- Real-time network simulation using existing interface
- Network metrics fed back to RL state space
- Realistic wireless network modeling

### 4. **AI-Network Co-optimization**
- Joint optimization of AI training and network resource allocation
- Task similarity information guides resource allocation decisions
- Continuous adaptation and learning

## 🧪 Experimental Configurations

### Predefined Scenarios
- **High Similarity**: Similar tasks (MNIST, CIFAR10) with high threshold (0.7)
- **Low Similarity**: Mixed tasks with low threshold (0.3) 
- **Heterogeneous**: All tasks (MNIST, CIFAR10, Air Quality) with medium threshold (0.5)
- **Independent**: Force independent training with very low threshold (0.1)

### Configurable Parameters
- Number of clients and rounds
- Task similarity threshold
- MAPPO hyperparameters
- Network type (WiFi, Ethernet)
- Task selection and priorities

## 📈 Performance Metrics

### Training Metrics
- Task-specific loss and accuracy
- Training time and resource utilization
- Model convergence rates

### Network Metrics  
- Throughput and latency
- Packet loss and transmission time
- Power consumption

### System Metrics
- Resource allocation efficiency
- MAPPO reward progression
- Task similarity utilization
- Overall system performance

## 🔍 Usage Examples

### Basic Multi-Task Training
```python
from multi_task_client import MultiTaskClient, TaskConfig, TaskType

# Create task configurations
tasks = {
    "mnist": TaskConfig("mnist", TaskType.IMAGE_CLASSIFICATION, "data/MNIST", "cnn"),
    "cifar10": TaskConfig("cifar10", TaskType.IMAGE_CLASSIFICATION, "data/cifar-10-batches-py", "resnet")
}

# Create client
client = MultiTaskClient(client_id=0, task_configs=tasks)

# Analyze task similarity
client.analyze_task_similarity()

# Get state for MAPPO
state = client.get_state_for_mappo()
```

### MAPPO Resource Allocation
```python
from mappo_allocator import MAPPOResourceAllocator

# Create allocator
allocator = MAPPOResourceAllocator(num_clients=5, num_tasks=3)

# Get resource allocations
allocations = allocator.get_resource_allocations(client_states)

# Update with training rewards
allocator.update_with_rewards(rewards, next_states, dones)
```

### Complete Experiment
```python
from run_multi_task_fl import create_experiment_config, run_experiment

# Create configuration
config = create_experiment_config(
    num_clients=5,
    num_rounds=50,
    similarity_threshold=0.5,
    tasks=["mnist", "cifar10", "airquality"]
)

# Run experiment
results = run_experiment(config, "my_experiment")
```

## 🎯 Research Contributions

1. **Task Correlation-Driven Federated Learning**: Novel integration of task similarity analysis with federated learning for intelligent resource management

2. **AI-Network Co-optimization**: Joint optimization of AI training performance and network resource utilization using multi-agent reinforcement learning

3. **Multi-Task Edge Learning**: Efficient handling of heterogeneous tasks on resource-constrained edge devices with adaptive resource allocation

4. **MAPPO-based Resource Management**: Application of multi-agent reinforcement learning for dynamic resource allocation in wireless federated learning environments

## 🤝 Contributing

This project builds upon existing federated learning and task similarity analysis frameworks. Contributions are welcome for:
- Additional task types and datasets
- Alternative resource allocation algorithms
- Enhanced network simulation models
- Performance optimization improvements

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
