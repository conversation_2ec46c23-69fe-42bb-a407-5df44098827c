#!/usr/bin/env python3
"""
Test script to verify the MAPPO action space modification
"""

import numpy as np
import logging
import numpy as np
from mappo_allocator import MAPPOResourceAllocator, MAPPOConfig
from multi_task_client import ResourceAllocation

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_action_space_modification():
    """Test the modified action space with task priority"""

    logger.info("Testing MAPPO action space modification with task priority...")

    # Test parameters
    num_clients = 3
    num_tasks = 2

    # Create MAPPO allocator
    config = MAPPOConfig()
    allocator = MAPPOResourceAllocator(num_clients, num_tasks, config)

    # Verify action dimension calculation
    expected_action_dim = num_tasks * 5  # 5 actions per task: select_ij, q_ij, B_ij, P_ij, Task_ij
    actual_action_dim = allocator.action_dim

    logger.info(f"Expected action dimension: {expected_action_dim}")
    logger.info(f"Actual action dimension: {actual_action_dim}")

    assert actual_action_dim == expected_action_dim, f"Action dimension mismatch: expected {expected_action_dim}, got {actual_action_dim}"

    # Test state dimension calculation
    expected_state_dim = num_tasks * 2 + 5 + 3 + 1  # task metrics + resource utilization + network + similarity
    actual_state_dim = allocator.state_dim

    logger.info(f"Expected state dimension: {expected_state_dim}")
    logger.info(f"Actual state dimension: {actual_state_dim}")

    assert actual_state_dim == expected_state_dim, f"State dimension mismatch: expected {expected_state_dim}, got {actual_state_dim}"

    # Test action to allocation conversion
    logger.info("Testing action to allocation conversion...")

    # Create a sample action vector
    sample_action = np.random.uniform(-1, 1, expected_action_dim)

    # Convert to allocation
    allocation = allocator._action_to_allocation(sample_action)

    # Verify allocation structure
    assert hasattr(allocation, 'select_ij'), "Missing select_ij field"
    assert hasattr(allocation, 'q_ij'), "Missing q_ij field"
    assert hasattr(allocation, 'B_ij'), "Missing B_ij field"
    assert hasattr(allocation, 'P_ij'), "Missing P_ij field"
    assert hasattr(allocation, 'Task_ij'), "Missing Task_ij field"

    # Verify each allocation has correct number of tasks
    assert len(allocation.select_ij) == num_tasks, f"select_ij should have {num_tasks} tasks"
    assert len(allocation.q_ij) == num_tasks, f"q_ij should have {num_tasks} tasks"
    assert len(allocation.B_ij) == num_tasks, f"B_ij should have {num_tasks} tasks"
    assert len(allocation.P_ij) == num_tasks, f"P_ij should have {num_tasks} tasks"
    assert len(allocation.Task_ij) == num_tasks, f"Task_ij should have {num_tasks} tasks"

    # Verify all values are in [0, 1] range
    for task_name in allocation.select_ij:
        assert 0 <= allocation.select_ij[task_name] <= 1, f"select_ij[{task_name}] out of range"
        assert 0 <= allocation.q_ij[task_name] <= 1, f"q_ij[{task_name}] out of range"
        assert 0 <= allocation.B_ij[task_name] <= 1, f"B_ij[{task_name}] out of range"
        assert 0 <= allocation.P_ij[task_name] <= 1, f"P_ij[{task_name}] out of range"
        assert 0 <= allocation.Task_ij[task_name] <= 1, f"Task_ij[{task_name}] out of range"

    logger.info("Action space modification test passed!")
    logger.info(f"Sample allocation:")
    logger.info(f"  select_ij: {allocation.select_ij}")
    logger.info(f"  q_ij: {allocation.q_ij}")
    logger.info(f"  B_ij: {allocation.B_ij}")
    logger.info(f"  P_ij: {allocation.P_ij}")
    logger.info(f"  Task_ij: {allocation.Task_ij}")


if __name__ == "__main__":
    test_action_space_modification()
    
    # Test action to allocation conversion
    logger.info("Testing action to allocation conversion...")
    
    # Create a sample action (normalized to [-1, 1])
    sample_action = np.random.uniform(-1, 1, actual_action_dim)
    logger.info(f"Sample action: {sample_action}")
    
    # Convert to allocation
    allocation = allocator._action_to_allocation(sample_action)
    
    # Verify allocation structure
    assert isinstance(allocation, ResourceAllocation), "Allocation should be ResourceAllocation instance"
    assert hasattr(allocation, 'select_ij'), "Allocation should have select_ij field"
    assert hasattr(allocation, 'q_ij'), "Allocation should have q_ij field"
    assert hasattr(allocation, 'B_ij'), "Allocation should have B_ij field"
    assert hasattr(allocation, 'P_ij'), "Allocation should have P_ij field"
    
    # Verify task mappings
    expected_tasks = [f"task_{i}" for i in range(num_tasks)]
    
    for task_name in expected_tasks:
        assert task_name in allocation.select_ij, f"Task {task_name} missing from select_ij"
        assert task_name in allocation.q_ij, f"Task {task_name} missing from q_ij"
        assert task_name in allocation.B_ij, f"Task {task_name} missing from B_ij"
        assert task_name in allocation.P_ij, f"Task {task_name} missing from P_ij"
        
        # Verify values are in [0, 1] range
        assert 0 <= allocation.select_ij[task_name] <= 1, f"select_ij[{task_name}] out of range"
        assert 0 <= allocation.q_ij[task_name] <= 1, f"q_ij[{task_name}] out of range"
        assert 0 <= allocation.B_ij[task_name] <= 1, f"B_ij[{task_name}] out of range"
        assert 0 <= allocation.P_ij[task_name] <= 1, f"P_ij[{task_name}] out of range"
    
    logger.info("Allocation structure verification passed!")
    
    # Print allocation details
    logger.info("Allocation details:")
    for task_name in expected_tasks:
        logger.info(f"  {task_name}:")
        logger.info(f"    select_ij: {allocation.select_ij[task_name]:.3f}")
        logger.info(f"    q_ij: {allocation.q_ij[task_name]:.3f}")
        logger.info(f"    B_ij: {allocation.B_ij[task_name]:.3f}")
        logger.info(f"    P_ij: {allocation.P_ij[task_name]:.3f}")
    
    # Test with multiple clients
    logger.info("Testing with multiple clients...")
    
    client_states = {}
    for client_id in range(num_clients):
        # Create dummy state (state_dim = num_tasks * 2 + 4 + 3 + 1)
        state_dim = allocator.state_dim
        client_states[client_id] = np.random.uniform(-1, 1, state_dim)
    
    # Get allocations for all clients
    allocations = allocator.get_resource_allocations(client_states, deterministic=True)
    
    assert len(allocations) == num_clients, f"Expected {num_clients} allocations, got {len(allocations)}"
    
    for client_id, allocation in allocations.items():
        logger.info(f"Client {client_id} allocation summary:")
        total_select = sum(allocation.select_ij.values())
        total_compute = sum(allocation.q_ij.values())
        total_bandwidth = sum(allocation.B_ij.values())
        total_power = sum(allocation.P_ij.values())
        
        logger.info(f"  Total selection: {total_select:.3f}")
        logger.info(f"  Total compute: {total_compute:.3f}")
        logger.info(f"  Total bandwidth: {total_bandwidth:.3f}")
        logger.info(f"  Total power: {total_power:.3f}")
    
    logger.info("All tests passed! Action space modification successful.")
    
    return True

if __name__ == "__main__":
    try:
        test_action_space_modification()
        print("✅ Action space modification test completed successfully!")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
